package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.entity.FrontUser;
import com.admin.service.FrontUserService;
import com.admin.model.query.FrontUserQuery;
import com.admin.model.dto.RechargeDTO;
import com.admin.common.util.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
 
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

@Slf4j
@RestController
@RequestMapping("/user")
@Validated
public class FrontUserController {
    
    @Autowired
    private FrontUserService frontUserService;
    @Autowired


    @Log(title = "用户信息", operType = "拓扑图")
    @GetMapping("/topology/tree")
    public R<?> getTopology() {
        return R.ok(frontUserService.getTeamTopology());
    }

    @Log(title = "用户信息", operType = "查询")
    @GetMapping("/list")
    public R<IPage<FrontUser>> list(@Validated FrontUserQuery query) {
        Page<FrontUser> page = new Page<>(query.getPage(), query.getLimit());
        return R.ok(frontUserService.getUserList(page, query));
    }
    
    @GetMapping("/{id}")
    public R<FrontUser> detail(@PathVariable Long id) {
        FrontUser user = frontUserService.getUserDetail(id);
        return R.ok(user);
    }
    @Log(title = "用户信息", operType = "修改")
    @PutMapping("/{id}/status/{status}")
    public R<?> updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        return frontUserService.updateUserStatus(id, status) ? R.ok() : R.fail();
    }

    @Log(title = "用户信息", operType = "修改激活状态")
    @PutMapping("/{id}/activated/{isActivated}")
    public R<?> updateActivatedStatus(@PathVariable Long id, @PathVariable Integer isActivated) {
        return frontUserService.updateUserActivatedStatus(id, isActivated) ? R.ok() : R.fail();
    }

    @Log(title = "用户信息", operType = "修改利润划转状态")
    @PutMapping("/{id}/profit-transfer/{profitTransferEnabled}")
    public R<?> updateProfitTransferStatus(@PathVariable Long id, @PathVariable Integer profitTransferEnabled) {
        return frontUserService.updateProfitTransferStatus(id, profitTransferEnabled) ? R.ok() : R.fail();
    }
    @Log(title = "用户信息", operType = "重置密码")
    @PutMapping("/{id}/reset")
    public R<?> resetPassword(@PathVariable Long id) {
        return frontUserService.resetPassword(id) ? R.ok() : R.fail();
    }

    @Log(title = "用户信息", operType = "充值")
    @PostMapping("/{id}/recharge")
    public R<?> recharge(
        @PathVariable Long id,
        @Validated @RequestBody RechargeDTO dto
    ) {

        return frontUserService.recharge(id, dto.getAmount(), dto.getRemark()) 
            ? R.ok() : R.fail("充值失败");
    }
    
    @GetMapping("/topology/tree/{email}")
    public R<?> getTopologyByEmail(@PathVariable String email) {
        return R.ok(frontUserService.getTeamTopologyByEmail(email));
    }

    @Log(title = "用户信息", operType = "删除")
    @DeleteMapping("/delete/{id}")
    public R<?> deleteUser(@PathVariable Long id) {
        try {
            // 1. 检查用户是否存在
            FrontUser user = frontUserService.getById(id);
            if (user == null) {
                return R.fail("用户不存在");
            }
            // 3. 检查用户是否有推荐的下级用户
            boolean hasReferrals = frontUserService.getBaseMapper().exists(
                new QueryWrapper<FrontUser>()
                    .eq("referrer_code", user.getShareCode())
            );
            if (hasReferrals) {
                return R.fail("该用户有推荐的下级用户，不能删除");
            }

            // 4. 检查用户账户余额
            if (user.getAvailableBalance() != null && 
                user.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0) {
                return R.fail("该用户账户余额不为0，不能删除");
            }

            // 5. 执行删除操作
            boolean success = frontUserService.removeById(id);
            if (success) {
                return R.ok("删除成功");
            } else {
                return R.fail("删除失败");
            }
        } catch (Exception e) {
            log.error("删除用户异常", e);
            return R.fail("删除用户失败: " + e.getMessage());
        }
    }
} 