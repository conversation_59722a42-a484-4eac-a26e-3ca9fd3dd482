<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.FrontUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.admin.entity.FrontUser">
        <id column="id" property="id" />
        <result column="user_no" property="userNo" />
        <result column="username" property="username" />
<!--        <result column="password" property="password" />-->
        <result column="email" property="email" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="share_code" property="shareCode" />
        <result column="referrer_code" property="referrerCode" />
        <result column="available_balance" property="availableBalance" />
        <result column="copy_trade_balance" property="copyTradeBalance" />
        <result column="commission_balance" property="commissionBalance" />

        <result column="profit_balance" property="profitBalance" />
        <result column="frozen_balance" property="frozenBalance" />
        <result column="team_total_count" property="teamTotalCount" />
        <result column="team_today_count" property="teamTodayCount" />
        <result column="status" property="status" />
        <result column="commission_rate" property="commissionRate" />
        <result column="copy_trade_frozen_status" property="copyTradeFrozenStatus" />
        <result column="cat_balance" property="catBalance" />
        <result column="total_recharge" property="totalRecharge" />
        <result column="is_activated" property="isActivated" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="referrer_name" property="referrerName" />
        <result column="referrer_share_code" property="referrerShareCode" />
        <result column="referrer_email" property="referrerEmail" />
        <result column="profit_transfer_enabled" property="profitTransferEnabled" />
        </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        u.id, u.user_no, u.username, u.password, u.email,  u.real_name, u.phone, u.share_code, u.referrer_code,    u.copy_trade_frozen_status, u.cat_balance,
        u.agent_level, u.available_balance, u.copy_trade_balance, u.commission_balance,  u.profit_balance, u.frozen_balance,
        u.team_total_count, u.team_today_count, u.status, u.commission_rate, u.total_recharge, u.is_activated, u.profit_transfer_enabled, u.create_time, u.update_time,
        u.is_leader, u.is_following, u.follow_start_time, u.reserve_amount, u.leader_id,
        r.username as referrer_name,r.share_code as referrer_share_code,r.email as referrer_email,
        l.username as leader_name, l.email as leader_email
    </sql>

    <!-- 根据用户ID更新可用余额 -->
    <update id="updateAvailableBalance">
        UPDATE front_user 
        SET available_balance = available_balance + #{amount}
        WHERE id = #{userId}
    </update>

    <!-- 根据用户ID更新可用余额 -->
    <update id="updateAvailableBalanceCZ">
        UPDATE front_user
        SET available_balance = available_balance + #{amount},cat_balance=cat_balance+#{amount} ,total_recharge=total_recharge+ #{amount}
        WHERE id = #{userId}
    </update>

    <!-- 更新用户跟单账户余额 -->
    <update id="updateCopyTradeBalance">
        UPDATE front_user
        SET copy_trade_balance = copy_trade_balance + #{amount}
        WHERE id = #{userId}
    </update>

    <!-- 更新用户储备金额 -->
    <update id="updateReserveAmount">
        UPDATE front_user
        SET reserve_amount = reserve_amount + #{amount}
        WHERE id = #{userId}
    </update>

    <!-- 更新用户激活状态 -->
    <update id="updateActivatedStatus">
        UPDATE front_user
        SET is_activated = #{isActivated}
        WHERE id = #{id}
    </update>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
         FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        WHERE u.phone = #{phone}
       
    </select>

    <!-- 根据用户编号查询用户 -->
    <select id="selectByUserNo" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        WHERE user_no = #{userNo}
    </select>

    <!-- 根据分享码查询用户 -->
    <select id="selectByShareCode" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        WHERE share_code = #{shareCode}
    </select>

    <!-- 分页查询用户列表 -->
    <select id="getUserList" resultType="com.admin.entity.FrontUser">
        SELECT 
            <include refid="Base_Column_List" />
        FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        LEFT JOIN front_user l ON u.leader_id = l.id
        <where>
            <if test="query != null">
                <if test="query.username != null and query.username != ''">
                    AND (u.username = #{query.username} OR u.phone = #{query.username})
                </if>
                <if test="query.userNo != null and query.userNo != ''">
                    AND u.user_no = #{query.userNo}
                </if>
                <if test="query.status != null">
                    AND u.status = #{query.status}
                </if>
                <if test="query.email != null and query.email != ''">
                    AND u.email = #{query.email}
                </if>

                <if test="query.shareCode != null and query.shareCode != ''">
                    AND u.share_code = #{query.shareCode}
                </if>
                <if test="query.referrerEmail != null and query.referrerEmail != ''">
                    AND EXISTS (SELECT 1 FROM front_user r WHERE r.email = #{query.referrerEmail} AND r.share_code = u.referrer_code)
                </if>
                <if test="query.startDate != null and query.startDate != ''">
                    AND DATE(u.create_time) >= #{query.startDate}
                </if>
                <if test="query.endDate != null and query.endDate != ''">
                    AND DATE(u.create_time) &lt;= #{query.endDate}
                </if>
            </if>
        </where>
        ORDER BY u.create_time DESC
    </select>

    <!-- 根据推荐码查询用户 -->
    <select id="selectByReferrerCode" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        WHERE referrer_code = #{referrerCode}
    </select>

    <select id="selectUserPage" resultType="com.admin.entity.FrontUser">
        SELECT 
            <include refid="Base_Column_List" />
        FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        <where>
            <if test="query.username != null and query.username != ''">
                AND (u.username = #{query.username} OR u.phone = #{query.username})
            </if>
            <if test="query.email != null and query.email != ''">
                AND u.email = #{query.email}
            </if>
            <if test="query.status != null">
                AND u.status = #{query.status}
            </if>
            <if test="query.startDate != null">
                AND u.create_time >= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND u.create_time &lt;= #{query.endDate}
            </if>
        </where>
        ORDER BY u.create_time DESC
    </select>

    <select id="getUserDetail" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        LEFT JOIN front_user l ON u.leader_id = l.id
        WHERE u.id = #{id}
    </select>

 

    <!-- 根据邮箱查询用户（包含代理级别） -->
    <select id="getUserByEmail" resultMap="BaseResultMap">
        SELECT 
            u.id, u.user_no, u.username, u.password, u.email, u.security_password, u.real_name, u.phone, u.share_code, u.referrer_code,
            u.agent_level, u.available_balance, u.copy_trade_balance, u.copy_trade_frozen_status, u.commission_balance, u.profit_balance, u.frozen_balance, u.cat_balance,
            u.team_total_count, u.team_today_count, u.status, u.commission_rate, u.total_recharge, u.is_activated, u.create_time, u.update_time,
            u.is_leader, u.is_following, u.follow_start_time, u.reserve_amount, u.leader_id,
            r.username as referrer_name, r.share_code as referrer_share_code, r.email as referrer_email,
            l.username as leader_name, l.email as leader_email
        FROM front_user u
        LEFT JOIN front_user r ON u.referrer_code = r.share_code
        LEFT JOIN front_user l ON u.leader_id = l.id
        WHERE u.email = #{email}
    </select>

    <!-- 获取顶级用户列表（包含代理级别） -->
    <select id="getTopUsers" resultMap="BaseResultMap">
        SELECT 
            u.id, u.user_no, u.username, u.password, u.email, u.security_password, u.real_name, u.phone, u.share_code, u.referrer_code,
            u.agent_level, u.available_balance, u.copy_trade_balance, u.copy_trade_frozen_status, u.commission_balance, u.profit_balance, u.frozen_balance, u.cat_balance,
            u.team_total_count, u.team_today_count, u.status, u.commission_rate, u.total_recharge, u.is_activated, u.create_time, u.update_time,
            r.username as referrer_name, r.share_code as referrer_share_code, r.email as referrer_email
        FROM front_user u 
        LEFT JOIN front_user r ON u.referrer_code = r.share_code    
        WHERE (u.referrer_code IS NULL OR u.referrer_code = '')
        ORDER BY u.create_time DESC
    </select>

    <!-- 获取直接下级用户列表 -->
    <select id="getSubordinateUsers" resultMap="BaseResultMap">
        SELECT 
            fu.id, fu.user_no, fu.username, fu.password, fu.email, fu.security_password, fu.real_name, fu.phone, fu.share_code, fu.referrer_code,
            fu.agent_level, fu.available_balance, fu.copy_trade_balance, fu.copy_trade_frozen_status, fu.commission_balance, fu.profit_balance, fu.frozen_balance, fu.cat_balance,
            fu.team_total_count, fu.team_today_count, fu.status, fu.commission_rate, fu.total_recharge, fu.is_activated, fu.create_time, fu.update_time,
            r.username as referrer_name, r.share_code as referrer_share_code, r.email as referrer_email
        FROM front_user fu 
        LEFT JOIN front_user r ON fu.referrer_code = r.share_code
        WHERE fu.referrer_code = #{shareCode}
    </select>

    <!-- 添加重置团队今日统计的方法 -->
    <update id="resetTeamTodayCount">
        UPDATE front_user 
        SET team_today_count = 0
    </update>

    <update id="updatePasswordById">
        UPDATE front_user
        SET password = #{password}
        WHERE id = #{id}
    </update>

    <!-- 更新用户利润划转状态 -->
    <update id="updateProfitTransferStatus">
        UPDATE front_user
        SET profit_transfer_enabled = #{profitTransferEnabled}
        WHERE id = #{userId}
    </update>

</mapper>




