{"ast": null, "code": "import _regeneratorRuntime from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport { getUserList, getUserDetail, updateUserStatus, updateUserActivatedStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels, updateUserLevel, updateUserBalance, updateUserGbDividend, deleteUser, updateUserProfitTransferStatus } from '@/api/user/user';\nimport { listUserWallet } from '@/api/user/wallet';\nimport { parseTime, formatDate } from '@/utils/date';\nimport axios from 'axios';\nexport default {\n  name: 'UserList',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        // 用户名/手机号\n        userNo: '',\n        // UID\n        status: '',\n        // 状态\n        shareCode: '',\n        // 邀请码\n        referrerEmail: '',\n        // 邀请人邮箱\n        email: '',\n        // 邮箱\n        dateRange: [],\n        startDate: '',\n        endDate: ''\n      },\n      loading: false,\n      total: 0,\n      tableData: [],\n      // 充值相关\n      rechargeVisible: false,\n      rechargeUser: {},\n      rechargeForm: {\n        amount: 100,\n        remark: ''\n      },\n      rechargeRules: {\n        amount: [{\n          required: true,\n          message: '请输入充值金额',\n          trigger: 'blur'\n        }]\n      },\n      // 详情相关\n      detailVisible: false,\n      detailUser: {\n        username: '',\n        phone: '',\n        realName: '',\n        teamCount: 0,\n        teamPerformance: 0,\n        createTime: '',\n        lastLoginTime: '',\n        balance: 0,\n        status: '1',\n        referrer: '',\n        inviteCode: '',\n        totalRecharge: 0,\n        totalWithdraw: 0,\n        commission: 0\n      },\n      // 银行卡相关\n      bankCardsVisible: false,\n      bankCardsLoading: false,\n      bankCards: [],\n      // 修改等级相关\n      changeLevelVisible: false,\n      currentUser: {},\n      levelForm: {\n        isManager: ''\n      },\n      // 修改余额相关\n      modifyBalanceVisible: false,\n      modifyBalanceForm: {\n        newBalance: 0\n      },\n      modifyBalanceRules: {\n        newBalance: [{\n          required: true,\n          message: '请输入新余额',\n          trigger: 'blur'\n        }]\n      },\n      walletDialogVisible: false,\n      walletList: [],\n      walletLoading: false\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  watch: {\n    // 监听日期范围变化\n    'listQuery.dateRange': function listQueryDateRange(val) {\n      if (val && val.length === 2) {\n        this.listQuery.startDate = formatDate(val[0]);\n        this.listQuery.endDate = formatDate(val[1]);\n      } else {\n        this.listQuery.startDate = '';\n        this.listQuery.endDate = '';\n      }\n    }\n  },\n  methods: {\n    // 获取列表数据\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              _context.next = 4;\n              return getUserList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0 || res.code === 200) {\n                // 确保数据存在\n                if (res.data) {\n                  _this.tableData = res.data.records || [];\n                  _this.total = res.data.total || 0;\n                } else {\n                  _this.tableData = [];\n                  _this.total = 0;\n                }\n              } else {\n                _this.$message.error(res.msg || '获取用户列表失败');\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取用户列表失败:', _context.t0);\n              _this.$message.error('获取用户列表失败');\n            case 12:\n              _context.prev = 12;\n              _this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 8, 12, 15]]);\n      }))();\n    },\n    // 搜索\n    handleSearch: function handleSearch() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    // 重置查询\n    resetQuery: function resetQuery() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        userNo: '',\n        // 重置UID\n        status: '',\n        isManager: '',\n        // 重置用户等级\n        shareCode: '',\n        // 重置邀请码\n        referrerEmail: '',\n        // 重置邀请人邮箱\n        email: '',\n        // 重置邮箱\n        dateRange: [],\n        startDate: '',\n        endDate: '',\n        contractAgreement: '',\n        // 重置合同状态\n        isGbDividend: '' // 重置GB分红状态\n      };\n      this.getList();\n    },\n    // 格式化数字\n    formatNumber: function formatNumber(num) {\n      return num ? num.toLocaleString() : '0';\n    },\n    // 获取等级名称\n    getLevelName: function getLevelName(level) {\n      var levelMap = {\n        0: '普通会员',\n        1: '合伙人',\n        2: '联创'\n      };\n      return levelMap[level] || '未知等级';\n    },\n    // 获取等级标签类型\n    getLevelType: function getLevelType(level) {\n      var typeMap = {\n        0: 'info',\n        1: 'success',\n        2: 'warning'\n      };\n      return typeMap[level] || 'info';\n    },\n    // 处理状态变更\n    handleStatusChange: function handleStatusChange(row) {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return updateUserStatus(row.id, row.status);\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this2.$message.success(\"\".concat(row.status === 1 ? '启用' : '禁用', \"\\u6210\\u529F\"));\n              } else {\n                row.status = row.status === 1 ? 0 : 1;\n                _this2.$message.error(res.msg || '操作失败');\n              }\n              _context2.next = 11;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              row.status = row.status === 1 ? 0 : 1;\n              _this2.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    // 处理激活状态变更\n    handleActivatedChange: function handleActivatedChange(row) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return updateUserActivatedStatus(row.id, row.isActivated);\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this3.$message.success(\"\".concat(row.isActivated === 1 ? '激活' : '取消激活', \"\\u6210\\u529F\"));\n              } else {\n                row.isActivated = row.isActivated === 1 ? 0 : 1;\n                _this3.$message.error(res.msg || '操作失败');\n              }\n              _context3.next = 11;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              row.isActivated = row.isActivated === 1 ? 0 : 1;\n              _this3.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    // 处理利润划转状态变更\n    handleProfitTransferChange: function handleProfitTransferChange(row) {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.prev = 0;\n              _context4.next = 3;\n              return updateUserProfitTransferStatus(row.id, row.profitTransferEnabled);\n            case 3:\n              res = _context4.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this4.$message.success(\"\".concat(row.profitTransferEnabled === 1 ? '开启' : '关闭', \"\\u5229\\u6DA6\\u5212\\u8F6C\\u6210\\u529F\"));\n              } else {\n                row.profitTransferEnabled = row.profitTransferEnabled === 1 ? 0 : 1;\n                _this4.$message.error(res.msg || '操作失败');\n              }\n              _context4.next = 11;\n              break;\n            case 7:\n              _context4.prev = 7;\n              _context4.t0 = _context4[\"catch\"](0);\n              row.profitTransferEnabled = row.profitTransferEnabled === 1 ? 0 : 1;\n              _this4.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[0, 7]]);\n      }))();\n    },\n    // 查看详情\n    handleDetail: function handleDetail(row) {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return getUserDetail(row.id);\n            case 3:\n              res = _context5.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this5.detailUser = res.data;\n                _this5.detailVisible = true;\n              } else {\n                _this5.$message.error(res.msg || '获取详情失败');\n              }\n              _context5.next = 10;\n              break;\n            case 7:\n              _context5.prev = 7;\n              _context5.t0 = _context5[\"catch\"](0);\n              _this5.$message.error('获取详情失败');\n            case 10:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 7]]);\n      }))();\n    },\n    // 打开充值对话框\n    handleRecharge: function handleRecharge(row) {\n      this.rechargeUser = row;\n      this.rechargeForm = {\n        amount: 100,\n        remark: ''\n      };\n      this.rechargeVisible = true;\n    },\n    // 提交充值\n    submitRecharge: function submitRecharge() {\n      var _this6 = this;\n      this.$refs.rechargeForm.validate(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n            while (1) switch (_context6.prev = _context6.next) {\n              case 0:\n                if (!valid) {\n                  _context6.next = 11;\n                  break;\n                }\n                _context6.prev = 1;\n                _context6.next = 4;\n                return rechargeUser(_this6.rechargeUser.id, _this6.rechargeForm);\n              case 4:\n                res = _context6.sent;\n                if (res.code === 0 || res.code === 200) {\n                  _this6.$message.success('充值成功');\n                  _this6.rechargeVisible = false;\n                  _this6.getList(); // 刷新列表\n                } else {\n                  _this6.$message.error(res.msg || '充值失败');\n                }\n                _context6.next = 11;\n                break;\n              case 8:\n                _context6.prev = 8;\n                _context6.t0 = _context6[\"catch\"](1);\n                _this6.$message.error('充值失败');\n              case 11:\n              case \"end\":\n                return _context6.stop();\n            }\n          }, _callee6, null, [[1, 8]]);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    // 分页相关\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');\n    },\n    // 格式化日期\n    formatDate: function formatDate(time) {\n      if (!time) return '';\n      return parseTime(time, 'yyyy-MM-dd');\n    },\n    // 重置密码\n    handleReset: function handleReset(row) {\n      var _this7 = this;\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.prev = 0;\n              _context7.next = 3;\n              return resetUserPassword(row.id);\n            case 3:\n              res = _context7.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this7.$message.success('密码重置成功');\n              } else {\n                _this7.$message.error(res.msg || '密码重置失败');\n              }\n              _context7.next = 10;\n              break;\n            case 7:\n              _context7.prev = 7;\n              _context7.t0 = _context7[\"catch\"](0);\n              _this7.$message.error('密码重置失败');\n            case 10:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消重置，不做任何操作\n      });\n    },\n    // 查看银行\n    handleBankCards: function handleBankCards(row) {\n      var _this8 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              _this8.bankCardsVisible = true;\n              _this8.bankCardsLoading = true;\n              _context8.prev = 2;\n              _context8.next = 5;\n              return getUserBankCards(row.id);\n            case 5:\n              res = _context8.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this8.bankCards = res.data || [];\n              } else {\n                _this8.$message.error(res.msg || '获取银行卡列表失败');\n              }\n              _context8.next = 13;\n              break;\n            case 9:\n              _context8.prev = 9;\n              _context8.t0 = _context8[\"catch\"](2);\n              console.error('获取银行卡失败:', _context8.t0); // 添加错误日志\n              _this8.$message.error('获取银行卡列表失败');\n            case 13:\n              _context8.prev = 13;\n              _this8.bankCardsLoading = false;\n              return _context8.finish(13);\n            case 16:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8, null, [[2, 9, 13, 16]]);\n      }))();\n    },\n    // 打开修改等级对话框\n    handleChangeLevel: function handleChangeLevel(row) {\n      this.currentUser = row;\n      this.levelForm.isManager = row.isManager;\n      this.changeLevelVisible = true;\n    },\n    // 提交修改等级\n    submitChangeLevel: function submitChangeLevel() {\n      var _this9 = this;\n      this.$refs.levelForm.validate(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n            while (1) switch (_context9.prev = _context9.next) {\n              case 0:\n                if (!valid) {\n                  _context9.next = 12;\n                  break;\n                }\n                _context9.prev = 1;\n                _context9.next = 4;\n                return updateUserLevel(_this9.currentUser.id, _this9.levelForm.isManager);\n              case 4:\n                res = _context9.sent;\n                if (res.code === 0) {\n                  _this9.$message.success('修改等级成功');\n                  _this9.changeLevelVisible = false;\n                  _this9.getList(); // 刷新列表\n                } else {\n                  _this9.$message.error(res.msg || '修改等级失败');\n                }\n                _context9.next = 12;\n                break;\n              case 8:\n                _context9.prev = 8;\n                _context9.t0 = _context9[\"catch\"](1);\n                console.error('修改等级失败:', _context9.t0);\n                _this9.$message.error('修改等级失败');\n              case 12:\n              case \"end\":\n                return _context9.stop();\n            }\n          }, _callee9, null, [[1, 8]]);\n        }));\n        return function (_x2) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    },\n    // 打开修改余额对话框\n    handleModifyBalance: function handleModifyBalance(row) {\n      this.currentUser = row;\n      this.modifyBalanceForm.newBalance = row.availableBalance;\n      this.modifyBalanceVisible = true;\n    },\n    // 提交修改余额\n    submitModifyBalance: function submitModifyBalance() {\n      var _this10 = this;\n      this.$refs.modifyBalanceForm.validate(/*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n            while (1) switch (_context10.prev = _context10.next) {\n              case 0:\n                if (!valid) {\n                  _context10.next = 14;\n                  break;\n                }\n                _context10.prev = 1;\n                console.log('准备修改余额:', {\n                  userId: _this10.currentUser.id,\n                  newBalance: _this10.modifyBalanceForm.newBalance\n                });\n                _context10.next = 5;\n                return updateUserBalance(_this10.currentUser.id, _this10.modifyBalanceForm.newBalance);\n              case 5:\n                res = _context10.sent;\n                console.log('修改余额响应:', res);\n                if (res.code === 0 || res.code === 200) {\n                  _this10.$message.success('修改余额成功');\n                  _this10.modifyBalanceVisible = false;\n                  _this10.getList(); // 刷新列表\n                } else {\n                  _this10.$message.error(res.msg || '修改余额失败');\n                }\n                _context10.next = 14;\n                break;\n              case 10:\n                _context10.prev = 10;\n                _context10.t0 = _context10[\"catch\"](1);\n                console.error('修改余额失败:', _context10.t0);\n                _this10.$message.error('修改余额失败');\n              case 14:\n              case \"end\":\n                return _context10.stop();\n            }\n          }, _callee10, null, [[1, 10]]);\n        }));\n        return function (_x3) {\n          return _ref4.apply(this, arguments);\n        };\n      }());\n    },\n    // 处理GB分红状态变更\n    handleGbDividendChange: function handleGbDividendChange(row) {\n      var _this11 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              _context11.prev = 0;\n              _context11.next = 3;\n              return updateUserGbDividend(row.id, row.isGbDividend);\n            case 3:\n              res = _context11.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this11.$message.success(\"\".concat(row.isGbDividend === 1 ? '开启' : '关闭', \"GB\\u5206\\u7EA2\\u6210\\u529F\"));\n              } else {\n                row.isGbDividend = row.isGbDividend === 1 ? 0 : 1;\n                _this11.$message.error(res.msg || '操作失败');\n              }\n              _context11.next = 11;\n              break;\n            case 7:\n              _context11.prev = 7;\n              _context11.t0 = _context11[\"catch\"](0);\n              row.isGbDividend = row.isGbDividend === 1 ? 0 : 1;\n              _this11.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11, null, [[0, 7]]);\n      }))();\n    },\n    // 处理删除用户\n    handleDelete: function handleDelete(row) {\n      var _this12 = this;\n      this.$confirm('确认要删除该用户吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee12() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              _context12.prev = 0;\n              _context12.next = 3;\n              return deleteUser(row.id);\n            case 3:\n              res = _context12.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this12.$message.success('删除成功');\n                _this12.getList(); // 刷新列表\n              } else {\n                _this12.$message.error(res.msg || '删除失败');\n              }\n              _context12.next = 10;\n              break;\n            case 7:\n              _context12.prev = 7;\n              _context12.t0 = _context12[\"catch\"](0);\n              _this12.$message.error('删除失败');\n            case 10:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee12, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消删除，不做任何操作\n      });\n    },\n    handleWalletList: function handleWalletList(row) {\n      var _this13 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee13() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee13$(_context13) {\n          while (1) switch (_context13.prev = _context13.next) {\n            case 0:\n              _this13.walletDialogVisible = true;\n              _this13.walletLoading = true;\n              _context13.prev = 2;\n              _context13.next = 5;\n              return listUserWallet({\n                userId: row.id,\n                pageNum: 1,\n                pageSize: 100\n              });\n            case 5:\n              res = _context13.sent;\n              if (res.code === 0 && res.data && res.data.records) {\n                _this13.walletList = res.data.records;\n              } else {\n                _this13.walletList = [];\n              }\n              _context13.next = 13;\n              break;\n            case 9:\n              _context13.prev = 9;\n              _context13.t0 = _context13[\"catch\"](2);\n              _this13.walletList = [];\n              _this13.$message.error('获取钱包列表失败');\n            case 13:\n              _this13.walletLoading = false;\n            case 14:\n            case \"end\":\n              return _context13.stop();\n          }\n        }, _callee13, null, [[2, 9]]);\n      }))();\n    },\n    formatAddress: function formatAddress(addr) {\n      if (!addr) return '';\n      if (addr.length <= 16) return addr;\n      return addr.slice(0, 6) + '...' + addr.slice(-6);\n    },\n    // 表格序号方法，保证翻页不间断\n    indexMethod: function indexMethod(index) {\n      return (this.listQuery.page - 1) * this.listQuery.limit + index + 1;\n    }\n  }\n};", "map": {"version": 3, "names": ["getUserList", "getUserDetail", "updateUserStatus", "updateUserActivatedStatus", "resetUserPassword", "rechargeUser", "getUserBankCards", "getAgentLevels", "updateUserLevel", "updateUserBalance", "updateUserGbDividend", "deleteUser", "updateUserProfitTransferStatus", "listUserWallet", "parseTime", "formatDate", "axios", "name", "data", "list<PERSON>uery", "page", "limit", "username", "userNo", "status", "shareCode", "referrerEmail", "email", "date<PERSON><PERSON><PERSON>", "startDate", "endDate", "loading", "total", "tableData", "rechargeVisible", "rechargeForm", "amount", "remark", "rechargeRules", "required", "message", "trigger", "detailVisible", "detailUser", "phone", "realName", "teamCount", "teamPerformance", "createTime", "lastLoginTime", "balance", "referrer", "inviteCode", "totalRecharge", "totalWithdraw", "commission", "bankCardsVisible", "bankCardsLoading", "bankCards", "changeLevelVisible", "currentUser", "levelForm", "is<PERSON>anager", "modifyBalanceVisible", "modifyBalanceForm", "newBalance", "modifyBalanceRules", "walletDialogVisible", "walletList", "walletLoading", "created", "getList", "watch", "listQueryDateRange", "val", "length", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "records", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleSearch", "reset<PERSON><PERSON>y", "contractAgreement", "isGbDividend", "formatNumber", "num", "toLocaleString", "getLevelName", "level", "levelMap", "getLevelType", "typeMap", "handleStatusChange", "row", "_this2", "_callee2", "_callee2$", "_context2", "id", "success", "concat", "handleActivatedChange", "_this3", "_callee3", "_callee3$", "_context3", "isActivated", "handleProfitTransferChange", "_this4", "_callee4", "_callee4$", "_context4", "profitTransferEnabled", "handleDetail", "_this5", "_callee5", "_callee5$", "_context5", "handleRecharge", "submit<PERSON>echarge", "_this6", "$refs", "validate", "_ref", "_callee6", "valid", "_callee6$", "_context6", "_x", "apply", "arguments", "handleSizeChange", "handleCurrentChange", "formatDateTime", "time", "handleReset", "_this7", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_callee7", "_callee7$", "_context7", "handleBankCards", "_this8", "_callee8", "_callee8$", "_context8", "handleChangeLevel", "submitChangeLevel", "_this9", "_ref3", "_callee9", "_callee9$", "_context9", "_x2", "handleModifyBalance", "availableBalance", "submitModifyBalance", "_this10", "_ref4", "_callee10", "_callee10$", "_context10", "log", "userId", "_x3", "handleGbDividendChange", "_this11", "_callee11", "_callee11$", "_context11", "handleDelete", "_this12", "_callee12", "_callee12$", "_context12", "handleWalletList", "_this13", "_callee13", "_callee13$", "_context13", "pageNum", "pageSize", "formatAddress", "addr", "slice", "indexMethod", "index"], "sources": ["src/views/user/list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.username\"\r\n              placeholder=\"用户名\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 180px\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.userNo\"\r\n              placeholder=\"UID\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 180px\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-select\r\n              v-model=\"listQuery.status\"\r\n              placeholder=\"账户状态\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            >\r\n              <el-option label=\"正常\" value=\"1\" />\r\n              <el-option label=\"禁用\" value=\"0\" />\r\n            </el-select>\r\n          </el-col>\r\n        \r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.shareCode\"\r\n              placeholder=\"邀请码\"\r\n              clearable\r\n              class=\"filter-item\"\r\n               style=\"width: 180px\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.referrerEmail\"\r\n              placeholder=\"邀请人邮箱\"\r\n              clearable\r\n              class=\"filter-item\"\r\n               style=\"width: 180px\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.email\"\r\n              placeholder=\"邮箱\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n           <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"listQuery.dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              class=\"filter-item date-range-picker\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n         \r\n          <el-col :span=\"18\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n            <!-- <el-button type=\"warning\" icon=\"el-icon-download\">导出</el-button> -->\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n       \r\n        <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\"\r\n          :index=\"indexMethod\" />\r\n        <el-table-column label=\"UID\" prop=\"userNo\" width=\"130\" align=\"center\" />\r\n        <el-table-column label=\"用户名称\" prop=\"username\" align=\"center\"  width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tooltip\r\n              :content=\"scope.row.username\"\r\n              placement=\"top\"\r\n              :disabled=\"!scope.row.username || scope.row.username.length <= 5\"\r\n            >\r\n              <span style=\"cursor: pointer;\">\r\n                {{ scope.row.username && scope.row.username.length > 5 ? scope.row.username.substring(0, 5) + '...' : scope.row.username }}\r\n              </span>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"头像\" prop=\"avatar\" align=\"center\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <img\r\n              v-if=\"scope.row.avatar\"\r\n              :src=\"scope.row.avatar\"\r\n              @error=\"e => e.target.src = require('@/assets/default.png')\"\r\n              style=\"width:40px;height:40px;border-radius:50%;object-fit:cover;\"\r\n            />\r\n            <img\r\n              v-else\r\n              :src=\"require('@/assets/default.png')\"\r\n              style=\"width:40px;height:40px;border-radius:50%;object-fit:cover;\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column label=\"手机号码\" prop=\"phone\" align=\"center\" width=\"120\" /> -->\r\n        <el-table-column label=\"邮箱\" prop=\"email\" align=\"center\" width=\"180\" />\r\n        <el-table-column label=\"推荐人\" align=\"center\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.referrerEmail\">\r\n              {{ scope.row.referrerEmail }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ scope.row.referrerShareCode }}</el-tag>\r\n            </div>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"分享码\" prop=\"shareCode\" align=\"center\" width=\"120\" />\r\n        \r\n        <el-table-column label=\"账户状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"激活状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.isActivated\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n\r\n              @change=\"handleActivatedChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"利润划转\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.profitTransferEnabled\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n              @change=\"handleProfitTransferChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"资金账户(USDT)  \" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.availableBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"跟单账户(USDT)\" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.copyTradeBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"跟单账户状态\" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ scope.row.copyTradeFrozenStatus === 1 ? '冻结' : '正常' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"佣金账户(USDT)\" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.commissionBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"利润账户(USDT)\" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.profitBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column label=\"跟单冻结账户(USDT)\" align=\"center\" width=\"170\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.usageFrozenBlance) }}</span>\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column label=\"提现冻结余额(USDT)\" align=\"center\" width=\"170\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.frozenBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"CAT币\" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.catBalance) }}</span>\r\n          </template>\r\n          </el-table-column>\r\n        <el-table-column label=\"是否带单员\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.isLeader === 1 ? 'success' : 'info'\" size=\"mini\">\r\n              {{ scope.row.isLeader === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"一键跟单\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.isFollowing === 1 ? 'success' : 'info'\" size=\"mini\">\r\n              {{ scope.row.isFollowing === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"跟单时间\" align=\"center\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.followStartTime ? formatDateTime(scope.row.followStartTime) : '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"储备金(USDT)\" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.reserveAmount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"带单员邮箱\" align=\"center\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.leaderEmail\">\r\n              {{ scope.row.leaderEmail }}\r\n              <br>\r\n              <small>{{ scope.row.leaderName }}</small>\r\n            </div>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"260\" fixed=\"right\">\r\n          <template v-slot=\"{ row }\">\r\n            <el-button type=\"text\" @click=\"handleDetail(row)\">详情</el-button>\r\n            <el-button type=\"text\" @click=\"handleRecharge(row)\">充值</el-button>\r\n            <el-button type=\"text\" @click=\"handleWalletList(row)\">充值地址列表</el-button>\r\n            <!-- <el-button type=\"text\" @click=\"handleBankCards(row)\">提现地址列表</el-button> -->\r\n            <el-button type=\"text\" style=\"color: #f56c6c\" @click=\"handleReset(row)\">重置密码</el-button>\r\n           \r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        />\r\n      </div>\r\n\r\n      <!-- 用户详情对话框 -->\r\n      <el-dialog\r\n        title=\"用户详情\"\r\n        :visible.sync=\"detailVisible\"\r\n        width=\"800px\"\r\n        :close-on-click-modal=\"false\"\r\n        custom-class=\"user-detail-dialog\"\r\n      >\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"UID\">{{ detailUser.userNo }}</el-descriptions-item>\r\n          <!-- <el-descriptions-item label=\"手机号\">{{ detailUser.phone }}</el-descriptions-item> -->\r\n          <el-descriptions-item label=\"邮箱\">{{ detailUser.email }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户名称\">\r\n            <el-tooltip \r\n              :content=\"detailUser.username\" \r\n              placement=\"top\" \r\n              :disabled=\"!detailUser.username || detailUser.username.length <= 5\"\r\n            >\r\n              <span style=\"cursor: pointer;\">\r\n                {{ detailUser.username && detailUser.username.length > 5 ? detailUser.username.substring(0, 5) + '...' : detailUser.username }}\r\n              </span>\r\n            </el-tooltip>\r\n          </el-descriptions-item> \r\n          <el-descriptions-item label=\"头像\">\r\n            <template v-if=\"detailUser.avatar\">\r\n              <img :src=\"detailUser.avatar\" @error=\"e => e.target.src = require('@/assets/default.png')\" style=\"width:60px;height:60px;border-radius:50%;object-fit:cover;\" />\r\n            </template>\r\n            <template v-else>\r\n              <img :src=\"require('@/assets/default.png')\" style=\"width:60px;height:60px;border-radius:50%;object-fit:cover;\" />\r\n            </template>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"佣金率\">{{ detailUser.commissionRate || '0' }}%</el-descriptions-item>\r\n          <el-descriptions-item label=\"累积总充值\">{{ formatNumber(detailUser.totalRecharge) || '0' }}USDT</el-descriptions-item>\r\n          <el-descriptions-item label=\"团队总人数\">{{ formatNumber(detailUser.teamTotalCount) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"团队新增人数\">{{ formatNumber(detailUser.teamTodayCount) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"注册时间\">{{ formatDateTime(detailUser.createTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"最后登录\">{{ formatDateTime(detailUser.updateTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"资金账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.availableBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"跟单账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.copyTradeBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"跟单账户状态\">\r\n            <span style=\"color: #67C23A\">{{ detailUser.copyTradeFrozenStatus === 1 ? '冻结' : '正常' }}</span>\r\n          </el-descriptions-item>\r\n            <el-descriptions-item label=\"佣金账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.commissionBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"利润账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.profitBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"CAT币\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.catBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"提现冻结余额\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.frozenBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"账户状态\">\r\n            <el-tag :type=\"detailUser.status === 1 ? 'success' : 'danger'\">\r\n              {{ detailUser.status === 1 ? '正常' : '禁用' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n         \r\n          <el-descriptions-item label=\"推荐人\">\r\n            <template v-if=\"detailUser.referrerEmail\">\r\n              {{ detailUser.referrerEmail }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ detailUser.referrerShareCode }}</el-tag>\r\n            </template>\r\n            <span v-else>-</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"邀请码\">{{ detailUser.shareCode || '-' }}</el-descriptions-item>\r\n\r\n          <!-- 新增的5个字段 -->\r\n          <el-descriptions-item label=\"是否带单员\">\r\n            <el-tag :type=\"detailUser.isLeader === 1 ? 'success' : 'info'\">\r\n              {{ detailUser.isLeader === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"一键跟单\">\r\n            <el-tag :type=\"detailUser.isFollowing === 1 ? 'success' : 'info'\">\r\n              {{ detailUser.isFollowing === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"跟单时间\">\r\n            {{ detailUser.followStartTime ? formatDateTime(detailUser.followStartTime) : '-' }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"储备金(USDT)\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.reserveAmount) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"带单员邮箱\">\r\n            {{ detailUser.leaderEmail || '-' }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"利润划转\">\r\n            <el-tag :type=\"detailUser.profitTransferEnabled === 1 ? 'success' : 'danger'\">\r\n              {{ detailUser.profitTransferEnabled === 1 ? '允许' : '禁止' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </el-dialog>\r\n\r\n      <!-- 用户充值对话框 -->\r\n      <el-dialog\r\n        title=\"用户充值\"\r\n        :visible.sync=\"rechargeVisible\"\r\n        width=\"500px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"rechargeForm\"\r\n          :model=\"rechargeForm\"\r\n          :rules=\"rechargeRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"用户手机号\">\r\n            <span>{{ rechargeUser.phone }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"账户资金余额\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(rechargeUser.availableBalance) || '0' }}USDT</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"充值金额\" prop=\"amount\">\r\n            <el-input-number\r\n              v-model=\"rechargeForm.amount\"\r\n              :precision=\"2\"\r\n              :step=\"100\"\r\n              style=\"width: 200px\"\r\n            />\r\n            <div style=\"color:#999;font-size:12px;\">可输入负数进行扣款</div>\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"remark\">\r\n            <el-input\r\n              v-model=\"rechargeForm.remark\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"请输入充值备注\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"rechargeVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitRecharge\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 银行卡列表对话框 -->\r\n      <el-dialog\r\n        title=\"提现钱包地址列表\"\r\n        :visible.sync=\"bankCardsVisible\"\r\n        width=\"1000px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-table\r\n          :data=\"bankCards\"\r\n          border\r\n          style=\"width: 100%\"\r\n          v-loading=\"bankCardsLoading\"\r\n        >\r\n          <el-table-column label=\"钱包地址\" prop=\"chainAddress\" align=\"center\" width=\"360\" />   \r\n          <el-table-column label=\"链名称\" prop=\"chainName\" align=\"center\" width=\"150\" />   \r\n          <el-table-column label=\"是否默认\" prop=\"isDefault\" align=\"center\" width=\"120\" />\r\n          <el-table-column label=\"创建时间\" prop=\"createTime\" align=\"center\" width=\"160\" />\r\n          <el-table-column label=\"更新时间\" prop=\"updateTime\" align=\"center\" width=\"160\" />\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"bankCardsVisible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n     \r\n\r\n      <!-- 修改余额对话框 -->\r\n      <el-dialog\r\n        title=\"修改账户余额\"\r\n        :visible.sync=\"modifyBalanceVisible\"\r\n        width=\"400px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"modifyBalanceForm\"\r\n          :model=\"modifyBalanceForm\"\r\n          :rules=\"modifyBalanceRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"用户名\">\r\n            <span>{{ currentUser.username }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号\">\r\n            <span>{{ currentUser.phone }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"当前余额\">\r\n            <span style=\"color: #67C23A\">¥{{ formatNumber(currentUser.availableBalance) || '0' }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"新余额\" prop=\"newBalance\">\r\n            <el-input-number\r\n              v-model=\"modifyBalanceForm.newBalance\"\r\n              :precision=\"2\"\r\n              :step=\"100\"\r\n              :controls-position=\"'right'\"\r\n              :min=\"-999999999\"\r\n              style=\"width: 200px\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"modifyBalanceVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitModifyBalance\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 钱包地址列表对话框 -->\r\n      <el-dialog\r\n        title=\"充值钱包地址列表\"\r\n        :visible.sync=\"walletDialogVisible\"\r\n        width=\"1000px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-table :data=\"walletList\" v-loading=\"walletLoading\" border>\r\n          <el-table-column label=\"链名称\" prop=\"chainName\" align=\"center\" />\r\n          <el-table-column label=\"链地址\" prop=\"chainAddress\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" :content=\"scope.row.chainAddress\" placement=\"top\">\r\n                <span style=\"cursor:pointer;\">\r\n                  {{ formatAddress(scope.row.chainAddress) }}\r\n                </span>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"BNB余额\" prop=\"bnbBalance\" align=\"center\" />\r\n          <el-table-column label=\"USDT余额\" prop=\"usdtBalance\" align=\"center\" />\r\n          <el-table-column label=\"创建时间\" prop=\"createTime\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.createTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"更新时间\" prop=\"updateTime\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.updateTime) }}\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"walletDialogVisible = false\">关闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getUserList, getUserDetail, updateUserStatus, updateUserActivatedStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels, updateUserLevel, updateUserBalance, updateUserGbDividend, deleteUser, updateUserProfitTransferStatus } from '@/api/user/user'\r\nimport { listUserWallet } from '@/api/user/wallet'\r\nimport { parseTime, formatDate } from '@/utils/date'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'UserList',\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',      // 用户名/手机号\r\n        userNo: '',        // UID\r\n        status: '',        // 状态\r\n        shareCode: '',     // 邀请码\r\n        referrerEmail: '', // 邀请人邮箱\r\n        email: '',         // 邮箱\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: '',\r\n\r\n      },\r\n \r\n      loading: false,\r\n      total: 0,\r\n      tableData: [],\r\n      // 充值相关\r\n      rechargeVisible: false,\r\n      rechargeUser: {},\r\n      rechargeForm: {\r\n        amount: 100,\r\n        remark: ''\r\n      },\r\n      rechargeRules: {\r\n        amount: [\r\n          { required: true, message: '请输入充值金额', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 详情相关\r\n      detailVisible: false,\r\n      detailUser: {\r\n        username: '',\r\n        phone: '',\r\n        realName: '',\r\n      \r\n        teamCount: 0,\r\n        teamPerformance: 0,\r\n        createTime: '',\r\n        lastLoginTime: '',\r\n        balance: 0,\r\n        status: '1',\r\n        referrer: '',\r\n        inviteCode: '',\r\n        totalRecharge: 0,\r\n        totalWithdraw: 0,\r\n        commission: 0\r\n      },\r\n      // 银行卡相关\r\n      bankCardsVisible: false,\r\n      bankCardsLoading: false,\r\n      bankCards: [],\r\n      // 修改等级相关\r\n      changeLevelVisible: false,\r\n      currentUser: {},\r\n      levelForm: {\r\n        isManager: ''\r\n      },\r\n   \r\n    \r\n      // 修改余额相关\r\n      modifyBalanceVisible: false,\r\n      modifyBalanceForm: {\r\n        newBalance: 0\r\n      },\r\n      modifyBalanceRules: {\r\n        newBalance: [\r\n          { required: true, message: '请输入新余额', trigger: 'blur' }\r\n        ]\r\n      },\r\n      walletDialogVisible: false,\r\n      walletList: [],\r\n      walletLoading: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n   \r\n  },\r\n  watch: {\r\n    // 监听日期范围变化\r\n    'listQuery.dateRange'(val) {\r\n      if (val && val.length === 2) {\r\n        this.listQuery.startDate = formatDate(val[0])\r\n        this.listQuery.endDate = formatDate(val[1])\r\n      } else {\r\n        this.listQuery.startDate = ''\r\n        this.listQuery.endDate = ''\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取列表数据\r\n    async getList() {\r\n      this.loading = true\r\n      try { \r\n        const res = await getUserList(this.listQuery)  \r\n        if (res.code === 0 || res.code === 200) {\r\n          // 确保数据存在\r\n          if (res.data) {\r\n            this.tableData = res.data.records || []\r\n            this.total = res.data.total || 0 \r\n          } else {\r\n            this.tableData = []\r\n            this.total = 0\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '获取用户列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取用户列表失败:', error)\r\n        this.$message.error('获取用户列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch() {\r\n      this.listQuery.page = 1\r\n      this.getList()\r\n    },\r\n\r\n    // 重置查询\r\n    resetQuery() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        userNo: '',        // 重置UID\r\n        status: '',\r\n        isManager: '',     // 重置用户等级\r\n        shareCode: '',     // 重置邀请码\r\n        referrerEmail: '', // 重置邀请人邮箱\r\n        email: '',         // 重置邮箱\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: '',\r\n        contractAgreement: '', // 重置合同状态\r\n        isGbDividend: ''      // 重置GB分红状态\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化数字\r\n    formatNumber(num) {\r\n      return num ? num.toLocaleString() : '0'\r\n    },\r\n\r\n    // 获取等级名称\r\n    getLevelName(level) {\r\n      const levelMap = {\r\n        0: '普通会员',\r\n        1: '合伙人',\r\n        2: '联创'\r\n      }\r\n      return levelMap[level] || '未知等级'\r\n    },\r\n\r\n    // 获取等级标签类型\r\n    getLevelType(level) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'warning'\r\n      }\r\n      return typeMap[level] || 'info'\r\n    },\r\n\r\n    // 处理状态变更\r\n    async handleStatusChange(row) {\r\n      try {\r\n        const res = await updateUserStatus(row.id, row.status)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.status === 1 ? '启用' : '禁用'}成功`)\r\n        } else {\r\n          row.status = row.status === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.status = row.status === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 处理激活状态变更\r\n    async handleActivatedChange(row) {\r\n      try {\r\n        const res = await updateUserActivatedStatus(row.id, row.isActivated)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.isActivated === 1 ? '激活' : '取消激活'}成功`)\r\n        } else {\r\n          row.isActivated = row.isActivated === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.isActivated = row.isActivated === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 处理利润划转状态变更\r\n    async handleProfitTransferChange(row) {\r\n      try {\r\n        const res = await updateUserProfitTransferStatus(row.id, row.profitTransferEnabled)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.profitTransferEnabled === 1 ? '开启' : '关闭'}利润划转成功`)\r\n        } else {\r\n          row.profitTransferEnabled = row.profitTransferEnabled === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.profitTransferEnabled = row.profitTransferEnabled === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 查看详情\r\n    async handleDetail(row) {\r\n      try {\r\n        const res = await getUserDetail(row.id)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.detailUser = res.data\r\n          this.detailVisible = true\r\n        } else {\r\n          this.$message.error(res.msg || '获取详情失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取详情失败')\r\n      }\r\n    },\r\n\r\n    // 打开充值对话框\r\n    handleRecharge(row) {\r\n      this.rechargeUser = row\r\n      this.rechargeForm = {\r\n        amount: 100,\r\n        remark: ''\r\n      }\r\n      this.rechargeVisible = true\r\n    },\r\n\r\n    // 提交充值\r\n    submitRecharge() {\r\n      this.$refs.rechargeForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await rechargeUser(this.rechargeUser.id, this.rechargeForm)\r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('充值成功')\r\n              this.rechargeVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '充值失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('充值失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 分页相关\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(time) {\r\n      if (!time) return ''\r\n      return parseTime(time, 'yyyy-MM-dd')\r\n    },\r\n\r\n    // 重置密码\r\n    handleReset(row) {\r\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await resetUserPassword(row.id)\r\n          if (res.code === 0 || res.code === 200) {\r\n            this.$message.success('密码重置成功')\r\n          } else {\r\n            this.$message.error(res.msg || '密码重置失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('密码重置失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消重置，不做任何操作\r\n      })\r\n    },\r\n\r\n    // 查看银行\r\n    async handleBankCards(row) {\r\n      \r\n      this.bankCardsVisible = true\r\n      this.bankCardsLoading = true\r\n      try {\r\n        const res = await getUserBankCards(row.id)\r\n       \r\n        if (res.code === 0 || res.code === 200) {\r\n          this.bankCards = res.data || []\r\n        } else {\r\n          this.$message.error(res.msg || '获取银行卡列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取银行卡失败:', error)  // 添加错误日志\r\n        this.$message.error('获取银行卡列表失败')\r\n      } finally {\r\n        this.bankCardsLoading = false\r\n      }\r\n    },\r\n \r\n    // 打开修改等级对话框\r\n    handleChangeLevel(row) {\r\n      this.currentUser = row\r\n      this.levelForm.isManager = row.isManager\r\n      this.changeLevelVisible = true\r\n    },\r\n    \r\n    // 提交修改等级\r\n    submitChangeLevel() {\r\n      this.$refs.levelForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await updateUserLevel(\r\n              this.currentUser.id,\r\n              this.levelForm.isManager\r\n            )\r\n            if (res.code === 0) {\r\n              this.$message.success('修改等级成功')\r\n              this.changeLevelVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '修改等级失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改等级失败:', error)\r\n            this.$message.error('修改等级失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 打开修改余额对话框\r\n    handleModifyBalance(row) {\r\n      this.currentUser = row\r\n      this.modifyBalanceForm.newBalance = row.availableBalance\r\n      this.modifyBalanceVisible = true\r\n    },\r\n\r\n    // 提交修改余额\r\n    submitModifyBalance() {\r\n      this.$refs.modifyBalanceForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            console.log('准备修改余额:', {\r\n              userId: this.currentUser.id,\r\n              newBalance: this.modifyBalanceForm.newBalance\r\n            })\r\n            \r\n            const res = await updateUserBalance(\r\n              this.currentUser.id, \r\n              this.modifyBalanceForm.newBalance\r\n            )\r\n            \r\n            console.log('修改余额响应:', res)\r\n            \r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('修改余额成功')\r\n              this.modifyBalanceVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '修改余额失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改余额失败:', error)\r\n            this.$message.error('修改余额失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理GB分红状态变更\r\n    async handleGbDividendChange(row) {\r\n      try {\r\n        const res = await updateUserGbDividend(row.id, row.isGbDividend)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.isGbDividend === 1 ? '开启' : '关闭'}GB分红成功`)\r\n        } else {\r\n          row.isGbDividend = row.isGbDividend === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.isGbDividend = row.isGbDividend === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 处理删除用户\r\n    handleDelete(row) {\r\n      this.$confirm('确认要删除该用户吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await deleteUser(row.id)\r\n          if (res.code === 0 || res.code === 200) {\r\n            this.$message.success('删除成功')\r\n            this.getList() // 刷新列表\r\n          } else {\r\n            this.$message.error(res.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消删除，不做任何操作\r\n      })\r\n    },\r\n\r\n    async handleWalletList(row) {\r\n      this.walletDialogVisible = true;\r\n      this.walletLoading = true;\r\n      try {\r\n        const res = await listUserWallet({ userId: row.id, pageNum: 1, pageSize: 100 });\r\n        if (res.code === 0 && res.data && res.data.records) {\r\n          this.walletList = res.data.records;\r\n        } else {\r\n          this.walletList = [];\r\n        }\r\n      } catch (e) {\r\n        this.walletList = [];\r\n        this.$message.error('获取钱包列表失败');\r\n      }\r\n      this.walletLoading = false;\r\n    },\r\n\r\n    formatAddress(addr) {\r\n      if (!addr) return '';\r\n      if (addr.length <= 16) return addr;\r\n      return addr.slice(0, 6) + '...' + addr.slice(-6);\r\n    },\r\n\r\n    // 表格序号方法，保证翻页不间断\r\n    indexMethod(index) {\r\n      return (this.listQuery.page - 1) * this.listQuery.limit + index + 1;\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 黑金主题整体覆盖 */\r\n.app-container {\r\n  background: #181818;\r\n  min-height: 100vh;\r\n}\r\n.el-card.box-card {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  border-radius: 12px;\r\n  color: #fff;\r\n  box-shadow: 0 4px 24px #000a;\r\n}\r\n.filter-container {\r\n  background: transparent;\r\n  .el-input__inner, .el-select .el-input__inner {\r\n    background: #232323 !important;\r\n    border: 1.5px solid #FFD700 !important;\r\n    color: #fff !important;\r\n    &::placeholder {\r\n      color: #b3b3b3 !important;\r\n    }\r\n  }\r\n  .el-select-dropdown {\r\n    background: #232323 !important;\r\n    color: #fff !important;\r\n    .el-select-dropdown__item {\r\n      color: #fff !important;\r\n      &:hover, &.selected {\r\n        color: #FFD700 !important;\r\n        background: #181818 !important;\r\n      }\r\n    }\r\n  }\r\n  .el-date-editor {\r\n    background: #232323 !important;\r\n    border: 1.5px solid #FFD700 !important;\r\n    color: #fff !important;\r\n    .el-input__inner {\r\n      background: #232323 !important;\r\n      color: #fff !important;\r\n      border: none !important;\r\n    }\r\n    .el-input__prefix, .el-input__suffix {\r\n      color: #FFD700 !important;\r\n    }\r\n  }\r\n  .el-date-editor .el-range-separator {\r\n    color: #FFD700 !important;\r\n  }\r\n  .el-button {\r\n    background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;\r\n    color: #181818 !important;\r\n    border: none !important;\r\n    font-weight: bold;\r\n    border-radius: 8px;\r\n    &:hover {\r\n      background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%) !important;\r\n      color: #000 !important;\r\n    }\r\n  }\r\n}\r\n.el-table {\r\n  background: #181818 !important;\r\n  color: #fff !important;\r\n  th, td {\r\n    background: #232323 !important;\r\n    color: #fff !important;\r\n    border-color: #FFD70033 !important;\r\n  }\r\n  td {\r\n    color: #fff !important;\r\n  }\r\n  td .is-disabled, td .is-disabled * {\r\n    color: #b3b3b3 !important;\r\n  }\r\n  .el-table__body tr:hover > td {\r\n    background: #292929 !important;\r\n  }\r\n  .el-table__header th {\r\n    background: #181818 !important;\r\n    color: #FFD700 !important;\r\n    font-weight: bold;\r\n  }\r\n}\r\n.el-table .el-button[type=\"text\"] {\r\n  color: #FFD700 !important;\r\n  font-weight: bold;\r\n}\r\n.el-tag.el-tag--info {\r\n  background: #232323 !important;\r\n  color: #FFD700 !important;\r\n  border: 1px solid #FFD700 !important;\r\n}\r\n.el-switch {\r\n  .el-switch__core {\r\n    background: #232323 !important;\r\n    border: 1.5px solid #FFD700 !important;\r\n  }\r\n  .el-switch__button {\r\n    background: #FFD700 !important;\r\n  }\r\n}\r\n.el-pagination {\r\n  background: transparent;\r\n  .el-pager li {\r\n    color: #fff !important;\r\n    background: #232323 !important;\r\n    border: 1px solid #FFD700 !important;\r\n    &.active {\r\n      color: #181818 !important;\r\n      background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;\r\n      border: 1px solid #FFD700 !important;\r\n    }\r\n  }\r\n  .el-pagination__total, .el-pagination__jump, .el-pagination__sizes {\r\n    color: #fff !important;\r\n  }\r\n}\r\n// 滚动条黑金化\r\n::-webkit-scrollbar {\r\n  height: 8px;\r\n  width: 8px;\r\n  background: #232323;\r\n}\r\n::-webkit-scrollbar-thumb {\r\n  background: #FFD70033;\r\n  border-radius: 4px;\r\n}\r\n// 弹窗黑金主题\r\n::v-deep .el-dialog {\r\n  background: #181818 !important;\r\n  border-radius: 12px;\r\n  .el-dialog__header {\r\n    color: #fff !important;\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    opacity: 1 !important;\r\n    border-bottom: 1px solid #FFD70033;\r\n  }\r\n  .el-dialog__title {\r\n    color: #fff !important;\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    opacity: 1 !important;\r\n  }\r\n  .el-dialog__footer {\r\n    .el-button--primary {\r\n      background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%);\r\n      color: #181818;\r\n      border: none;\r\n      font-weight: bold;\r\n      &:hover {\r\n        background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%);\r\n        color: #000;\r\n      }\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    color: #FFD700;\r\n  }\r\n  .el-input__inner {\r\n    background: #232323;\r\n    border: 1.5px solid #FFD700;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.filter-container {\r\n  padding: 10px 0 10px 0;\r\n\r\n  .filter-row {\r\n    // margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  .filter-item {\r\n    width: 100%;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .date-range-picker {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-button {\r\n    margin-right: 15px;\r\n    margin-top: 5px;\r\n  }\r\n\r\n  .el-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n// 修改对话框样式\r\n.user-detail-dialog {\r\n  ::v-deep .el-dialog__body {\r\n    padding: 10px 20px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__header {\r\n    padding: 15px 20px 10px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__footer {\r\n    padding: 10px 20px 15px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  margin: 0;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding-top: 0;\r\n}\r\n\r\n// 日期选择器弹窗黑金化\r\n::v-deep .el-picker-panel {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  .el-date-table th {\r\n    color: #FFD700 !important;\r\n  }\r\n  .el-date-table td {\r\n    color: #fff !important;\r\n    &.in-range {\r\n      background: #FFD70022 !important;\r\n    }\r\n    &.current, &.today {\r\n      color: #FFD700 !important;\r\n      font-weight: bold;\r\n    }\r\n    &.available:hover {\r\n      background: #FFD70033 !important;\r\n      color: #FFD700 !important;\r\n    }\r\n  }\r\n  .el-picker-panel__footer {\r\n    background: #232323 !important;\r\n    border-top: 1px solid #FFD70033 !important;\r\n    .el-button--text, .el-button--default {\r\n      color: #FFD700 !important;\r\n    }\r\n  }\r\n}\r\n\r\n// 强化表格内容字体色\r\n::v-deep .el-table td, ::v-deep .el-table td * {\r\n  color: #fff !important;\r\n}\r\n\r\n.el-table__fixed-body-wrapper td,\r\n.el-table__fixed-body-wrapper td *,\r\n.el-table__fixed .cell,\r\n.el-table__fixed .cell * {\r\n  color: #fff !important;\r\n  -webkit-text-fill-color: #fff !important;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table__body td,\r\n.el-table__body td *,\r\n.el-table__body .cell,\r\n.el-table__body .cell * {\r\n  color: #fff !important;\r\n  -webkit-text-fill-color: #fff !important;\r\n}\r\n.el-table__body td .is-disabled,\r\n.el-table__body td .is-disabled *,\r\n.el-table__body td[style*='color: #ccc'],\r\n.el-table__body td[style*='color: #e0e0e0'] {\r\n  color: #b3b3b3 !important;\r\n  -webkit-text-fill-color: #b3b3b3 !important;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n/* 黑金主题 el-descriptions-item label 全覆盖 */\r\n.el-descriptions__label,\r\n.el-descriptions-item__label,\r\n.el-descriptions-item__cell.is-label {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n  font-weight: bold !important;\r\n  border-right: 1px solid #FFD70033 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;AA0gBA,SAAAA,WAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,yBAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,oBAAA,EAAAC,UAAA,EAAAC,8BAAA;AACA,SAAAC,cAAA;AACA,SAAAC,SAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QAAA;QACAC,MAAA;QAAA;QACAC,MAAA;QAAA;QACAC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;MAEA;MAEAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACA7B,YAAA;MACA8B,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,aAAA;QACAF,MAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,aAAA;MACAC,UAAA;QACArB,QAAA;QACAsB,KAAA;QACAC,QAAA;QAEAC,SAAA;QACAC,eAAA;QACAC,UAAA;QACAC,aAAA;QACAC,OAAA;QACA1B,MAAA;QACA2B,QAAA;QACAC,UAAA;QACAC,aAAA;QACAC,aAAA;QACAC,UAAA;MACA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,kBAAA;MACAC,WAAA;MACAC,SAAA;QACAC,SAAA;MACA;MAGA;MACAC,oBAAA;MACAC,iBAAA;QACAC,UAAA;MACA;MACAC,kBAAA;QACAD,UAAA,GACA;UAAA1B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA0B,mBAAA;MACAC,UAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EAEA;EACAC,KAAA;IACA;IACA,gCAAAC,mBAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA,CAAAC,MAAA;QACA,KAAAxD,SAAA,CAAAU,SAAA,GAAAd,UAAA,CAAA2D,GAAA;QACA,KAAAvD,SAAA,CAAAW,OAAA,GAAAf,UAAA,CAAA2D,GAAA;MACA;QACA,KAAAvD,SAAA,CAAAU,SAAA;QACA,KAAAV,SAAA,CAAAW,OAAA;MACA;IACA;EACA;EACA8C,OAAA;IACA;IACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAA9C,OAAA;cAAAsD,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAvF,WAAA,CAAA6E,KAAA,CAAA1D,SAAA;YAAA;cAAA+D,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA;gBACA,IAAAP,GAAA,CAAAhE,IAAA;kBACA2D,KAAA,CAAA5C,SAAA,GAAAiD,GAAA,CAAAhE,IAAA,CAAAwE,OAAA;kBACAb,KAAA,CAAA7C,KAAA,GAAAkD,GAAA,CAAAhE,IAAA,CAAAc,KAAA;gBACA;kBACA6C,KAAA,CAAA5C,SAAA;kBACA4C,KAAA,CAAA7C,KAAA;gBACA;cACA;gBACA6C,KAAA,CAAAc,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,cAAAP,QAAA,CAAAS,EAAA;cACAjB,KAAA,CAAAc,QAAA,CAAAC,KAAA;YAAA;cAAAP,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAA9C,OAAA;cAAA,OAAAsD,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IAEA;IAEA;IACAiB,YAAA,WAAAA,aAAA;MACA,KAAA/E,SAAA,CAAAC,IAAA;MACA,KAAAmD,OAAA;IACA;IAEA;IACA4B,UAAA,WAAAA,WAAA;MACA,KAAAhF,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QAAA;QACAC,MAAA;QACAsC,SAAA;QAAA;QACArC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAsE,iBAAA;QAAA;QACAC,YAAA;MACA;MACA,KAAA9B,OAAA;IACA;IAEA;IACA+B,YAAA,WAAAA,aAAAC,GAAA;MACA,OAAAA,GAAA,GAAAA,GAAA,CAAAC,cAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAC,KAAA;MACA,IAAAC,QAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,KAAA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAAF,KAAA;MACA,IAAAG,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAH,KAAA;IACA;IAEA;IACAI,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAAlC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiC,SAAA;QAAA,IAAA/B,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAA5B,IAAA;cAAA,OAEArF,gBAAA,CAAA6G,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAAvF,MAAA;YAAA;cAAA0D,GAAA,GAAAiC,SAAA,CAAA3B,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAuB,MAAA,CAAArB,QAAA,CAAA0B,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAAvF,MAAA;cACA;gBACAuF,GAAA,CAAAvF,MAAA,GAAAuF,GAAA,CAAAvF,MAAA;gBACAwF,MAAA,CAAArB,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAsB,SAAA,CAAA5B,IAAA;cAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAArB,EAAA,GAAAqB,SAAA;cAEAJ,GAAA,CAAAvF,MAAA,GAAAuF,GAAA,CAAAvF,MAAA;cACAwF,MAAA,CAAArB,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAuB,SAAA,CAAAlB,IAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAEA;IAEA;IACAM,qBAAA,WAAAA,sBAAAR,GAAA;MAAA,IAAAS,MAAA;MAAA,OAAA1C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyC,SAAA;QAAA,IAAAvC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;YAAA;cAAAoC,SAAA,CAAArC,IAAA;cAAAqC,SAAA,CAAApC,IAAA;cAAA,OAEApF,yBAAA,CAAA4G,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAAa,WAAA;YAAA;cAAA1C,GAAA,GAAAyC,SAAA,CAAAnC,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA+B,MAAA,CAAA7B,QAAA,CAAA0B,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAAa,WAAA;cACA;gBACAb,GAAA,CAAAa,WAAA,GAAAb,GAAA,CAAAa,WAAA;gBACAJ,MAAA,CAAA7B,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA8B,SAAA,CAAApC,IAAA;cAAA;YAAA;cAAAoC,SAAA,CAAArC,IAAA;cAAAqC,SAAA,CAAA7B,EAAA,GAAA6B,SAAA;cAEAZ,GAAA,CAAAa,WAAA,GAAAb,GAAA,CAAAa,WAAA;cACAJ,MAAA,CAAA7B,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA+B,SAAA,CAAA1B,IAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IAEA;IAEA;IACAI,0BAAA,WAAAA,2BAAAd,GAAA;MAAA,IAAAe,MAAA;MAAA,OAAAhD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+C,SAAA;QAAA,IAAA7C,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA6C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3C,IAAA,GAAA2C,SAAA,CAAA1C,IAAA;YAAA;cAAA0C,SAAA,CAAA3C,IAAA;cAAA2C,SAAA,CAAA1C,IAAA;cAAA,OAEA3E,8BAAA,CAAAmG,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAAmB,qBAAA;YAAA;cAAAhD,GAAA,GAAA+C,SAAA,CAAAzC,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAqC,MAAA,CAAAnC,QAAA,CAAA0B,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAAmB,qBAAA;cACA;gBACAnB,GAAA,CAAAmB,qBAAA,GAAAnB,GAAA,CAAAmB,qBAAA;gBACAJ,MAAA,CAAAnC,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAoC,SAAA,CAAA1C,IAAA;cAAA;YAAA;cAAA0C,SAAA,CAAA3C,IAAA;cAAA2C,SAAA,CAAAnC,EAAA,GAAAmC,SAAA;cAEAlB,GAAA,CAAAmB,qBAAA,GAAAnB,GAAA,CAAAmB,qBAAA;cACAJ,MAAA,CAAAnC,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAqC,SAAA,CAAAhC,IAAA;UAAA;QAAA,GAAA8B,QAAA;MAAA;IAEA;IAEA;IACAI,YAAA,WAAAA,aAAApB,GAAA;MAAA,IAAAqB,MAAA;MAAA,OAAAtD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqD,SAAA;QAAA,IAAAnD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAmD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,IAAA,GAAAiD,SAAA,CAAAhD,IAAA;YAAA;cAAAgD,SAAA,CAAAjD,IAAA;cAAAiD,SAAA,CAAAhD,IAAA;cAAA,OAEAtF,aAAA,CAAA8G,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAqD,SAAA,CAAA/C,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA2C,MAAA,CAAAzF,UAAA,GAAAuC,GAAA,CAAAhE,IAAA;gBACAkH,MAAA,CAAA1F,aAAA;cACA;gBACA0F,MAAA,CAAAzC,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA0C,SAAA,CAAAhD,IAAA;cAAA;YAAA;cAAAgD,SAAA,CAAAjD,IAAA;cAAAiD,SAAA,CAAAzC,EAAA,GAAAyC,SAAA;cAEAH,MAAA,CAAAzC,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA2C,SAAA,CAAAtC,IAAA;UAAA;QAAA,GAAAoC,QAAA;MAAA;IAEA;IAEA;IACAG,cAAA,WAAAA,eAAAzB,GAAA;MACA,KAAA1G,YAAA,GAAA0G,GAAA;MACA,KAAA5E,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,KAAAH,eAAA;IACA;IAEA;IACAuG,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAxG,YAAA,CAAAyG,QAAA;QAAA,IAAAC,IAAA,GAAA/D,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8D,SAAAC,KAAA;UAAA,IAAA7D,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA6D,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA3D,IAAA,GAAA2D,SAAA,CAAA1D,IAAA;cAAA;gBAAA,KACAwD,KAAA;kBAAAE,SAAA,CAAA1D,IAAA;kBAAA;gBAAA;gBAAA0D,SAAA,CAAA3D,IAAA;gBAAA2D,SAAA,CAAA1D,IAAA;gBAAA,OAEAlF,YAAA,CAAAqI,MAAA,CAAArI,YAAA,CAAA+G,EAAA,EAAAsB,MAAA,CAAAvG,YAAA;cAAA;gBAAA+C,GAAA,GAAA+D,SAAA,CAAAzD,IAAA;gBACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACAiD,MAAA,CAAA/C,QAAA,CAAA0B,OAAA;kBACAqB,MAAA,CAAAxG,eAAA;kBACAwG,MAAA,CAAAnE,OAAA;gBACA;kBACAmE,MAAA,CAAA/C,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAoD,SAAA,CAAA1D,IAAA;gBAAA;cAAA;gBAAA0D,SAAA,CAAA3D,IAAA;gBAAA2D,SAAA,CAAAnD,EAAA,GAAAmD,SAAA;gBAEAP,MAAA,CAAA/C,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAqD,SAAA,CAAAhD,IAAA;YAAA;UAAA,GAAA6C,QAAA;QAAA,CAGA;QAAA,iBAAAI,EAAA;UAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA3E,GAAA;MACA,KAAAvD,SAAA,CAAAE,KAAA,GAAAqD,GAAA;MACA,KAAAH,OAAA;IACA;IACA+E,mBAAA,WAAAA,oBAAA5E,GAAA;MACA,KAAAvD,SAAA,CAAAC,IAAA,GAAAsD,GAAA;MACA,KAAAH,OAAA;IACA;IAEA;IACAgF,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,OAAA1I,SAAA,CAAA0I,IAAA;IACA;IAEA;IACAzI,UAAA,WAAAA,WAAAyI,IAAA;MACA,KAAAA,IAAA;MACA,OAAA1I,SAAA,CAAA0I,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA1C,GAAA;MAAA,IAAA2C,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAAjF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgF,SAAA;QAAA,IAAA9E,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5E,IAAA,GAAA4E,SAAA,CAAA3E,IAAA;YAAA;cAAA2E,SAAA,CAAA5E,IAAA;cAAA4E,SAAA,CAAA3E,IAAA;cAAA,OAEAnF,iBAAA,CAAA2G,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAgF,SAAA,CAAA1E,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAiE,MAAA,CAAA/D,QAAA,CAAA0B,OAAA;cACA;gBACAqC,MAAA,CAAA/D,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAqE,SAAA,CAAA3E,IAAA;cAAA;YAAA;cAAA2E,SAAA,CAAA5E,IAAA;cAAA4E,SAAA,CAAApE,EAAA,GAAAoE,SAAA;cAEAR,MAAA,CAAA/D,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAsE,SAAA,CAAAjE,IAAA;UAAA;QAAA,GAAA+D,QAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IAEA;IACAG,eAAA,WAAAA,gBAAApD,GAAA;MAAA,IAAAqD,MAAA;MAAA,OAAAtF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqF,SAAA;QAAA,IAAAnF,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAmF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjF,IAAA,GAAAiF,SAAA,CAAAhF,IAAA;YAAA;cAEA6E,MAAA,CAAA5G,gBAAA;cACA4G,MAAA,CAAA3G,gBAAA;cAAA8G,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAAhF,IAAA;cAAA,OAEAjF,gBAAA,CAAAyG,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAqF,SAAA,CAAA/E,IAAA;cAEA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA2E,MAAA,CAAA1G,SAAA,GAAAwB,GAAA,CAAAhE,IAAA;cACA;gBACAkJ,MAAA,CAAAzE,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA0E,SAAA,CAAAhF,IAAA;cAAA;YAAA;cAAAgF,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAAzE,EAAA,GAAAyE,SAAA;cAEAxE,OAAA,CAAAH,KAAA,aAAA2E,SAAA,CAAAzE,EAAA;cACAsE,MAAA,CAAAzE,QAAA,CAAAC,KAAA;YAAA;cAAA2E,SAAA,CAAAjF,IAAA;cAEA8E,MAAA,CAAA3G,gBAAA;cAAA,OAAA8G,SAAA,CAAAvE,MAAA;YAAA;YAAA;cAAA,OAAAuE,SAAA,CAAAtE,IAAA;UAAA;QAAA,GAAAoE,QAAA;MAAA;IAEA;IAEA;IACAG,iBAAA,WAAAA,kBAAAzD,GAAA;MACA,KAAAnD,WAAA,GAAAmD,GAAA;MACA,KAAAlD,SAAA,CAAAC,SAAA,GAAAiD,GAAA,CAAAjD,SAAA;MACA,KAAAH,kBAAA;IACA;IAEA;IACA8G,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAA/B,KAAA,CAAA9E,SAAA,CAAA+E,QAAA;QAAA,IAAA+B,KAAA,GAAA7F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4F,SAAA7B,KAAA;UAAA,IAAA7D,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA0F,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;cAAA;gBAAA,KACAwD,KAAA;kBAAA+B,SAAA,CAAAvF,IAAA;kBAAA;gBAAA;gBAAAuF,SAAA,CAAAxF,IAAA;gBAAAwF,SAAA,CAAAvF,IAAA;gBAAA,OAEA/E,eAAA,CACAkK,MAAA,CAAA9G,WAAA,CAAAwD,EAAA,EACAsD,MAAA,CAAA7G,SAAA,CAAAC,SACA;cAAA;gBAHAoB,GAAA,GAAA4F,SAAA,CAAAtF,IAAA;gBAIA,IAAAN,GAAA,CAAAO,IAAA;kBACAiF,MAAA,CAAA/E,QAAA,CAAA0B,OAAA;kBACAqD,MAAA,CAAA/G,kBAAA;kBACA+G,MAAA,CAAAnG,OAAA;gBACA;kBACAmG,MAAA,CAAA/E,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAiF,SAAA,CAAAvF,IAAA;gBAAA;cAAA;gBAAAuF,SAAA,CAAAxF,IAAA;gBAAAwF,SAAA,CAAAhF,EAAA,GAAAgF,SAAA;gBAEA/E,OAAA,CAAAH,KAAA,YAAAkF,SAAA,CAAAhF,EAAA;gBACA4E,MAAA,CAAA/E,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAkF,SAAA,CAAA7E,IAAA;YAAA;UAAA,GAAA2E,QAAA;QAAA,CAGA;QAAA,iBAAAG,GAAA;UAAA,OAAAJ,KAAA,CAAAxB,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACA4B,mBAAA,WAAAA,oBAAAjE,GAAA;MACA,KAAAnD,WAAA,GAAAmD,GAAA;MACA,KAAA/C,iBAAA,CAAAC,UAAA,GAAA8C,GAAA,CAAAkE,gBAAA;MACA,KAAAlH,oBAAA;IACA;IAEA;IACAmH,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAxC,KAAA,CAAA3E,iBAAA,CAAA4E,QAAA;QAAA,IAAAwC,KAAA,GAAAtG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqG,UAAAtC,KAAA;UAAA,IAAA7D,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAmG,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAjG,IAAA,GAAAiG,UAAA,CAAAhG,IAAA;cAAA;gBAAA,KACAwD,KAAA;kBAAAwC,UAAA,CAAAhG,IAAA;kBAAA;gBAAA;gBAAAgG,UAAA,CAAAjG,IAAA;gBAEAS,OAAA,CAAAyF,GAAA;kBACAC,MAAA,EAAAN,OAAA,CAAAvH,WAAA,CAAAwD,EAAA;kBACAnD,UAAA,EAAAkH,OAAA,CAAAnH,iBAAA,CAAAC;gBACA;gBAAAsH,UAAA,CAAAhG,IAAA;gBAAA,OAEA9E,iBAAA,CACA0K,OAAA,CAAAvH,WAAA,CAAAwD,EAAA,EACA+D,OAAA,CAAAnH,iBAAA,CAAAC,UACA;cAAA;gBAHAiB,GAAA,GAAAqG,UAAA,CAAA/F,IAAA;gBAKAO,OAAA,CAAAyF,GAAA,YAAAtG,GAAA;gBAEA,IAAAA,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACA0F,OAAA,CAAAxF,QAAA,CAAA0B,OAAA;kBACA8D,OAAA,CAAApH,oBAAA;kBACAoH,OAAA,CAAA5G,OAAA;gBACA;kBACA4G,OAAA,CAAAxF,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAA0F,UAAA,CAAAhG,IAAA;gBAAA;cAAA;gBAAAgG,UAAA,CAAAjG,IAAA;gBAAAiG,UAAA,CAAAzF,EAAA,GAAAyF,UAAA;gBAEAxF,OAAA,CAAAH,KAAA,YAAA2F,UAAA,CAAAzF,EAAA;gBACAqF,OAAA,CAAAxF,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAA2F,UAAA,CAAAtF,IAAA;YAAA;UAAA,GAAAoF,SAAA;QAAA,CAGA;QAAA,iBAAAK,GAAA;UAAA,OAAAN,KAAA,CAAAjC,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAuC,sBAAA,WAAAA,uBAAA5E,GAAA;MAAA,IAAA6E,OAAA;MAAA,OAAA9G,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6G,UAAA;QAAA,IAAA3G,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA2G,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAzG,IAAA,GAAAyG,UAAA,CAAAxG,IAAA;YAAA;cAAAwG,UAAA,CAAAzG,IAAA;cAAAyG,UAAA,CAAAxG,IAAA;cAAA,OAEA7E,oBAAA,CAAAqG,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAAV,YAAA;YAAA;cAAAnB,GAAA,GAAA6G,UAAA,CAAAvG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAmG,OAAA,CAAAjG,QAAA,CAAA0B,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAAV,YAAA;cACA;gBACAU,GAAA,CAAAV,YAAA,GAAAU,GAAA,CAAAV,YAAA;gBACAuF,OAAA,CAAAjG,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAkG,UAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,UAAA,CAAAzG,IAAA;cAAAyG,UAAA,CAAAjG,EAAA,GAAAiG,UAAA;cAEAhF,GAAA,CAAAV,YAAA,GAAAU,GAAA,CAAAV,YAAA;cACAuF,OAAA,CAAAjG,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAmG,UAAA,CAAA9F,IAAA;UAAA;QAAA,GAAA4F,SAAA;MAAA;IAEA;IAEA;IACAG,YAAA,WAAAA,aAAAjF,GAAA;MAAA,IAAAkF,OAAA;MACA,KAAAtC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAAjF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkH,UAAA;QAAA,IAAAhH,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAgH,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9G,IAAA,GAAA8G,UAAA,CAAA7G,IAAA;YAAA;cAAA6G,UAAA,CAAA9G,IAAA;cAAA8G,UAAA,CAAA7G,IAAA;cAAA,OAEA5E,UAAA,CAAAoG,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAkH,UAAA,CAAA5G,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAwG,OAAA,CAAAtG,QAAA,CAAA0B,OAAA;gBACA4E,OAAA,CAAA1H,OAAA;cACA;gBACA0H,OAAA,CAAAtG,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAuG,UAAA,CAAA7G,IAAA;cAAA;YAAA;cAAA6G,UAAA,CAAA9G,IAAA;cAAA8G,UAAA,CAAAtG,EAAA,GAAAsG,UAAA;cAEAH,OAAA,CAAAtG,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAwG,UAAA,CAAAnG,IAAA;UAAA;QAAA,GAAAiG,SAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IAEAG,gBAAA,WAAAA,iBAAAtF,GAAA;MAAA,IAAAuF,OAAA;MAAA,OAAAxH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuH,UAAA;QAAA,IAAArH,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAqH,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnH,IAAA,GAAAmH,UAAA,CAAAlH,IAAA;YAAA;cACA+G,OAAA,CAAAnI,mBAAA;cACAmI,OAAA,CAAAjI,aAAA;cAAAoI,UAAA,CAAAnH,IAAA;cAAAmH,UAAA,CAAAlH,IAAA;cAAA,OAEA1E,cAAA;gBAAA4K,MAAA,EAAA1E,GAAA,CAAAK,EAAA;gBAAAsF,OAAA;gBAAAC,QAAA;cAAA;YAAA;cAAAzH,GAAA,GAAAuH,UAAA,CAAAjH,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAhE,IAAA,IAAAgE,GAAA,CAAAhE,IAAA,CAAAwE,OAAA;gBACA4G,OAAA,CAAAlI,UAAA,GAAAc,GAAA,CAAAhE,IAAA,CAAAwE,OAAA;cACA;gBACA4G,OAAA,CAAAlI,UAAA;cACA;cAAAqI,UAAA,CAAAlH,IAAA;cAAA;YAAA;cAAAkH,UAAA,CAAAnH,IAAA;cAAAmH,UAAA,CAAA3G,EAAA,GAAA2G,UAAA;cAEAH,OAAA,CAAAlI,UAAA;cACAkI,OAAA,CAAA3G,QAAA,CAAAC,KAAA;YAAA;cAEA0G,OAAA,CAAAjI,aAAA;YAAA;YAAA;cAAA,OAAAoI,UAAA,CAAAxG,IAAA;UAAA;QAAA,GAAAsG,SAAA;MAAA;IACA;IAEAK,aAAA,WAAAA,cAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAA,IAAA,CAAAlI,MAAA,eAAAkI,IAAA;MACA,OAAAA,IAAA,CAAAC,KAAA,iBAAAD,IAAA,CAAAC,KAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,aAAA7L,SAAA,CAAAC,IAAA,aAAAD,SAAA,CAAAE,KAAA,GAAA2L,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}