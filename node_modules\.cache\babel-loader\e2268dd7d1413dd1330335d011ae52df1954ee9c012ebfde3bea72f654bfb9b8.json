{"ast": null, "code": "import _objectSpread from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport request from '@/utils/request';\n\n// 获取用户列表\nexport function getUserList(params) {\n  // 转换日期范围\n  var query = _objectSpread({}, params);\n  if (query.dateRange && query.dateRange.length === 2) {\n    query.startDate = query.dateRange[0];\n    query.endDate = query.dateRange[1];\n  }\n  delete query.dateRange;\n  return request({\n    url: '/user/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 获取用户详情\nexport function getUserDetail(id) {\n  return request({\n    url: \"/user/\".concat(id),\n    method: 'get'\n  });\n}\n\n// 更新用户状态\nexport function updateUserStatus(id, status) {\n  return request({\n    url: \"/user/\".concat(id, \"/status/\").concat(status),\n    method: 'put'\n  });\n}\n\n// 更新用户激活状态\nexport function updateUserActivatedStatus(id, isActivated) {\n  return request({\n    url: \"/user/\".concat(id, \"/activated/\").concat(isActivated),\n    method: 'put'\n  });\n}\n\n// 重置用户密码\nexport function resetUserPassword(id) {\n  return request({\n    url: \"/user/\".concat(id, \"/reset\"),\n    method: 'put'\n  });\n}\n\n// 用户充值\nexport function rechargeUser(id, data) {\n  return request({\n    url: \"/user/\".concat(id, \"/recharge\"),\n    method: 'post',\n    data: data\n  });\n}\n\n// 获取用户银行卡列表\nexport function getUserBankCards(userId) {\n  return request({\n    url: \"/user/\".concat(userId, \"/bank-cards\"),\n    method: 'get'\n  });\n}\n\n// 获取用户团队拓扑结构\nexport function getTeamTopology() {\n  return request({\n    url: '/user/topology/tree',\n    method: 'get'\n  });\n}\n\n// 获取指定用户的团队拓扑结构\nexport function getTeamTopologyByPhone(phone) {\n  return request({\n    url: \"/user/topology/tree/\".concat(phone),\n    method: 'get'\n  });\n}\n\n// 获取代理等级列表\nexport function getAgentLevels() {\n  return request({\n    url: '/agent/levels',\n    method: 'get'\n  });\n}\n\n// 更新用户代理等级\nexport function updateUserLevel(userId, isManager) {\n  return request({\n    url: \"/user/\".concat(userId, \"/level\"),\n    method: 'put',\n    data: {\n      isManager: isManager\n    }\n  });\n}\n\n// 修改用户余额\nexport function updateUserBalance(userId, newBalance) {\n  return request({\n    url: \"/user/\".concat(userId, \"/balance\"),\n    method: 'put',\n    data: {\n      newBalance: Number(newBalance) // 确保转换为数字\n    }\n  });\n}\n\n// 更新用户GB分红状态\nexport function updateUserGbDividend(userId, isGbDividend) {\n  return request({\n    url: '/user/updateGbDividend',\n    method: 'put',\n    data: {\n      userId: userId,\n      isGbDividend: isGbDividend\n    }\n  });\n}\n\n// 删除用户\nexport function deleteUser(userId) {\n  return request({\n    url: \"/user/delete/\".concat(userId),\n    method: 'delete'\n  });\n}\n\n// 根据邮箱查询团队拓扑\nexport function getTeamTopologyByEmail(email) {\n  return request({\n    url: \"/user/topology/tree/\".concat(email),\n    method: 'get'\n  });\n}\n\n// 更新用户利润划转状态\nexport function updateUserProfitTransferStatus(userId, profitTransferEnabled) {\n  return request({\n    url: \"/user/\".concat(userId, \"/profit-transfer/\").concat(profitTransferEnabled),\n    method: 'put'\n  });\n}", "map": {"version": 3, "names": ["request", "getUserList", "params", "query", "_objectSpread", "date<PERSON><PERSON><PERSON>", "length", "startDate", "endDate", "url", "method", "getUserDetail", "id", "concat", "updateUserStatus", "status", "updateUserActivatedStatus", "isActivated", "resetUserPassword", "rechargeUser", "data", "getUserBankCards", "userId", "getTeamTopology", "getTeamTopologyByPhone", "phone", "getAgentLevels", "updateUserLevel", "is<PERSON>anager", "updateUserBalance", "newBalance", "Number", "updateUserGbDividend", "isGbDividend", "deleteUser", "getTeamTopologyByEmail", "email", "updateUserProfitTransferStatus", "profitTransferEnabled"], "sources": ["E:/最新项目文件/交易所/adminweb/src/api/user/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取用户列表\r\nexport function getUserList(params) {\r\n  // 转换日期范围\r\n  const query = { ...params }\r\n  if (query.dateRange && query.dateRange.length === 2) {\r\n    query.startDate = query.dateRange[0]\r\n    query.endDate = query.dateRange[1]\r\n  }\r\n  delete query.dateRange\r\n\r\n  return request({\r\n    url: '/user/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 获取用户详情\r\nexport function getUserDetail(id) {\r\n  return request({\r\n    url: `/user/${id}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新用户状态\r\nexport function updateUserStatus(id, status) {\r\n  return request({\r\n    url: `/user/${id}/status/${status}`,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 更新用户激活状态\r\nexport function updateUserActivatedStatus(id, isActivated) {\r\n  return request({\r\n    url: `/user/${id}/activated/${isActivated}`,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 重置用户密码\r\nexport function resetUserPassword(id) {\r\n  return request({\r\n    url: `/user/${id}/reset`,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 用户充值\r\nexport function rechargeUser(id, data) {\r\n  return request({\r\n    url: `/user/${id}/recharge`,\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取用户银行卡列表\r\nexport function getUserBankCards(userId) {\r\n  \r\n  return request({\r\n    url: `/user/${userId}/bank-cards`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取用户团队拓扑结构\r\nexport function getTeamTopology() {\r\n  return request({\r\n    url: '/user/topology/tree',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取指定用户的团队拓扑结构\r\nexport function getTeamTopologyByPhone(phone) {\r\n  return request({\r\n    url: `/user/topology/tree/${phone}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取代理等级列表\r\nexport function getAgentLevels() {\r\n  return request({\r\n    url: '/agent/levels',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新用户代理等级\r\nexport function updateUserLevel(userId, isManager) {\r\n  return request({\r\n    url: `/user/${userId}/level`,\r\n    method: 'put',\r\n    data: { isManager }\r\n  })\r\n}\r\n\r\n// 修改用户余额\r\nexport function updateUserBalance(userId, newBalance) {\r\n  return request({\r\n    url: `/user/${userId}/balance`,\r\n    method: 'put',\r\n    data: { \r\n      newBalance: Number(newBalance) // 确保转换为数字\r\n    }\r\n  })\r\n}\r\n\r\n// 更新用户GB分红状态\r\nexport function updateUserGbDividend(userId, isGbDividend) {\r\n  return request({\r\n    url: '/user/updateGbDividend',\r\n    method: 'put',\r\n    data: {\r\n      userId,\r\n      isGbDividend\r\n    }\r\n  })\r\n}\r\n\r\n// 删除用户\r\nexport function deleteUser(userId) {\r\n  return request({\r\n    url: `/user/delete/${userId}`,\r\n    method: 'delete'\r\n  })\r\n} \r\n\r\n// 根据邮箱查询团队拓扑\r\nexport function getTeamTopologyByEmail(email) {\r\n  return request({\r\n    url: `/user/topology/tree/${email}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新用户利润划转状态\r\nexport function updateUserProfitTransferStatus(userId, profitTransferEnabled) {\r\n  return request({\r\n    url: `/user/${userId}/profit-transfer/${profitTransferEnabled}`,\r\n    method: 'put'\r\n  })\r\n}"], "mappings": ";;;AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC;EACA,IAAMC,KAAK,GAAAC,aAAA,KAAQF,MAAM,CAAE;EAC3B,IAAIC,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IACnDH,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC;IACpCF,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC;EACpC;EACA,OAAOF,KAAK,CAACE,SAAS;EAEtB,OAAOL,OAAO,CAAC;IACbS,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAEC;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAOZ,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,CAAE;IAClBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,gBAAgBA,CAACF,EAAE,EAAEG,MAAM,EAAE;EAC3C,OAAOf,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,cAAAC,MAAA,CAAWE,MAAM,CAAE;IACnCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,yBAAyBA,CAACJ,EAAE,EAAEK,WAAW,EAAE;EACzD,OAAOjB,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,iBAAAC,MAAA,CAAcI,WAAW,CAAE;IAC3CP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,iBAAiBA,CAACN,EAAE,EAAE;EACpC,OAAOZ,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,WAAQ;IACxBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,YAAYA,CAACP,EAAE,EAAEQ,IAAI,EAAE;EACrC,OAAOpB,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWD,EAAE,cAAW;IAC3BF,MAAM,EAAE,MAAM;IACdU,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAEvC,OAAOtB,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWS,MAAM,gBAAa;IACjCZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,eAAeA,CAAA,EAAG;EAChC,OAAOvB,OAAO,CAAC;IACbS,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,OAAOzB,OAAO,CAAC;IACbS,GAAG,yBAAAI,MAAA,CAAyBY,KAAK,CAAE;IACnCf,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgB,cAAcA,CAAA,EAAG;EAC/B,OAAO1B,OAAO,CAAC;IACbS,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASiB,eAAeA,CAACL,MAAM,EAAEM,SAAS,EAAE;EACjD,OAAO5B,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWS,MAAM,WAAQ;IAC5BZ,MAAM,EAAE,KAAK;IACbU,IAAI,EAAE;MAAEQ,SAAS,EAATA;IAAU;EACpB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAACP,MAAM,EAAEQ,UAAU,EAAE;EACpD,OAAO9B,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWS,MAAM,aAAU;IAC9BZ,MAAM,EAAE,KAAK;IACbU,IAAI,EAAE;MACJU,UAAU,EAAEC,MAAM,CAACD,UAAU,CAAC,CAAC;IACjC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,oBAAoBA,CAACV,MAAM,EAAEW,YAAY,EAAE;EACzD,OAAOjC,OAAO,CAAC;IACbS,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbU,IAAI,EAAE;MACJE,MAAM,EAANA,MAAM;MACNW,YAAY,EAAZA;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACZ,MAAM,EAAE;EACjC,OAAOtB,OAAO,CAAC;IACbS,GAAG,kBAAAI,MAAA,CAAkBS,MAAM,CAAE;IAC7BZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASyB,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,OAAOpC,OAAO,CAAC;IACbS,GAAG,yBAAAI,MAAA,CAAyBuB,KAAK,CAAE;IACnC1B,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS2B,8BAA8BA,CAACf,MAAM,EAAEgB,qBAAqB,EAAE;EAC5E,OAAOtC,OAAO,CAAC;IACbS,GAAG,WAAAI,MAAA,CAAWS,MAAM,uBAAAT,MAAA,CAAoByB,qBAAqB,CAAE;IAC/D5B,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}