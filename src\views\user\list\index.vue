<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="filter-container">
        <el-row :gutter="20" class="filter-row">
          <el-col :span="3">
            <el-input
              v-model.trim="listQuery.username"
              placeholder="用户名"
              clearable
              class="filter-item"
              style="width: 180px"
            />
          </el-col>
          <el-col :span="3">
            <el-input
              v-model.trim="listQuery.userNo"
              placeholder="UID"
              clearable
              class="filter-item"
              style="width: 180px"
            />
          </el-col>
          <el-col :span="3">
            <el-select
              v-model="listQuery.status"
              placeholder="账户状态"
              clearable
              class="filter-item"
            >
              <el-option label="正常" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-col>
        
          <el-col :span="3">
            <el-input
              v-model.trim="listQuery.shareCode"
              placeholder="邀请码"
              clearable
              class="filter-item"
               style="width: 180px"
            />
          </el-col>
          <el-col :span="3">
            <el-input
              v-model.trim="listQuery.referrerEmail"
              placeholder="邀请人邮箱"
              clearable
              class="filter-item"
               style="width: 180px"
            />
          </el-col>
          <el-col :span="4">
            <el-input
              v-model.trim="listQuery.email"
              placeholder="邮箱"
              clearable
              class="filter-item"
            />
          </el-col>
           <el-col :span="6">
            <el-date-picker
              v-model="listQuery.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="filter-item date-range-picker"
            />
          </el-col>
        </el-row>
        <el-row :gutter="20" class="filter-row">
         
          <el-col :span="18">
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            <el-button type="success" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            <!-- <el-button type="warning" icon="el-icon-download">导出</el-button> -->
          </el-col>
        </el-row>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
       
        <el-table-column label="序号" type="index" width="60" align="center"
          :index="indexMethod" />
        <el-table-column label="UID" prop="userNo" width="130" align="center" />
        <el-table-column label="用户名称" prop="username" align="center"  width="120">
          <template slot-scope="scope">
            <el-tooltip
              :content="scope.row.username"
              placement="top"
              :disabled="!scope.row.username || scope.row.username.length <= 5"
            >
              <span style="cursor: pointer;">
                {{ scope.row.username && scope.row.username.length > 5 ? scope.row.username.substring(0, 5) + '...' : scope.row.username }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="头像" prop="avatar" align="center" width="80">
          <template slot-scope="scope">
            <img
              v-if="scope.row.avatar"
              :src="scope.row.avatar"
              @error="e => e.target.src = require('@/assets/default.png')"
              style="width:40px;height:40px;border-radius:50%;object-fit:cover;"
            />
            <img
              v-else
              :src="require('@/assets/default.png')"
              style="width:40px;height:40px;border-radius:50%;object-fit:cover;"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column label="手机号码" prop="phone" align="center" width="120" /> -->
        <el-table-column label="邮箱" prop="email" align="center" width="180" />
        <el-table-column label="推荐人" align="center" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.referrerEmail">
              {{ scope.row.referrerEmail }}
              <el-tag size="mini" type="info">{{ scope.row.referrerShareCode }}</el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="分享码" prop="shareCode" align="center" width="120" />
        
        <el-table-column label="账户状态" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="激活状态" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isActivated"
              :active-value="1"
              :inactive-value="0"

              @change="handleActivatedChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="利润划转" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.profitTransferEnabled"
              :active-value="1"
              :inactive-value="0"
              @change="handleProfitTransferChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="资金账户(USDT)  " align="center" width="130">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.availableBalance) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="跟单账户(USDT)" align="center" width="130">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.copyTradeBalance) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="跟单账户状态" align="center" width="130">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ scope.row.copyTradeFrozenStatus === 1 ? '冻结' : '正常' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="佣金账户(USDT)" align="center" width="130">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.commissionBalance) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="利润账户(USDT)" align="center" width="130">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.profitBalance) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="跟单冻结账户(USDT)" align="center" width="170">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.usageFrozenBlance) }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="提现冻结余额(USDT)" align="center" width="170">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.frozenBalance) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="CAT币" align="center" width="130">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.catBalance) }}</span>
          </template>
          </el-table-column>
        <el-table-column label="是否带单员" align="center" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isLeader === 1 ? 'success' : 'info'" size="mini">
              {{ scope.row.isLeader === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="一键跟单" align="center" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isFollowing === 1 ? 'success' : 'info'" size="mini">
              {{ scope.row.isFollowing === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="跟单时间" align="center" width="150">
          <template slot-scope="scope">
            {{ scope.row.followStartTime ? formatDateTime(scope.row.followStartTime) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="储备金(USDT)" align="center" width="130">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.reserveAmount) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="带单员邮箱" align="center" width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.leaderEmail">
              {{ scope.row.leaderEmail }}
              <br>
              <small>{{ scope.row.leaderName }}</small>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="260" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" @click="handleDetail(row)">详情</el-button>
            <el-button type="text" @click="handleRecharge(row)">充值</el-button>
            <el-button type="text" @click="handleWalletList(row)">充值地址列表</el-button>
            <!-- <el-button type="text" @click="handleBankCards(row)">提现地址列表</el-button> -->
            <el-button type="text" style="color: #f56c6c" @click="handleReset(row)">重置密码</el-button>
           
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="listQuery.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>

      <!-- 用户详情对话框 -->
      <el-dialog
        title="用户详情"
        :visible.sync="detailVisible"
        width="800px"
        :close-on-click-modal="false"
        custom-class="user-detail-dialog"
      >
        <el-descriptions :column="2" border>
          <el-descriptions-item label="UID">{{ detailUser.userNo }}</el-descriptions-item>
          <!-- <el-descriptions-item label="手机号">{{ detailUser.phone }}</el-descriptions-item> -->
          <el-descriptions-item label="邮箱">{{ detailUser.email }}</el-descriptions-item>
          <el-descriptions-item label="用户名称">
            <el-tooltip 
              :content="detailUser.username" 
              placement="top" 
              :disabled="!detailUser.username || detailUser.username.length <= 5"
            >
              <span style="cursor: pointer;">
                {{ detailUser.username && detailUser.username.length > 5 ? detailUser.username.substring(0, 5) + '...' : detailUser.username }}
              </span>
            </el-tooltip>
          </el-descriptions-item> 
          <el-descriptions-item label="头像">
            <template v-if="detailUser.avatar">
              <img :src="detailUser.avatar" @error="e => e.target.src = require('@/assets/default.png')" style="width:60px;height:60px;border-radius:50%;object-fit:cover;" />
            </template>
            <template v-else>
              <img :src="require('@/assets/default.png')" style="width:60px;height:60px;border-radius:50%;object-fit:cover;" />
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="佣金率">{{ detailUser.commissionRate || '0' }}%</el-descriptions-item>
          <el-descriptions-item label="累积总充值">{{ formatNumber(detailUser.totalRecharge) || '0' }}USDT</el-descriptions-item>
          <el-descriptions-item label="团队总人数">{{ formatNumber(detailUser.teamTotalCount) || '0' }}</el-descriptions-item>
          <el-descriptions-item label="团队新增人数">{{ formatNumber(detailUser.teamTodayCount) || '0' }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatDateTime(detailUser.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatDateTime(detailUser.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="资金账户">
            <span style="color: #67C23A">{{ formatNumber(detailUser.availableBalance) || '0' }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="跟单账户">
            <span style="color: #67C23A">{{ formatNumber(detailUser.copyTradeBalance) || '0' }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="跟单账户状态">
            <span style="color: #67C23A">{{ detailUser.copyTradeFrozenStatus === 1 ? '冻结' : '正常' }}</span>
          </el-descriptions-item>
            <el-descriptions-item label="佣金账户">
            <span style="color: #67C23A">{{ formatNumber(detailUser.commissionBalance) || '0' }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="利润账户">
            <span style="color: #67C23A">{{ formatNumber(detailUser.profitBalance) || '0' }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="CAT币">
            <span style="color: #67C23A">{{ formatNumber(detailUser.catBalance) || '0' }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="提现冻结余额">
            <span style="color: #67C23A">{{ formatNumber(detailUser.frozenBalance) || '0' }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="账户状态">
            <el-tag :type="detailUser.status === 1 ? 'success' : 'danger'">
              {{ detailUser.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
         
          <el-descriptions-item label="推荐人">
            <template v-if="detailUser.referrerEmail">
              {{ detailUser.referrerEmail }}
              <el-tag size="mini" type="info">{{ detailUser.referrerShareCode }}</el-tag>
            </template>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="邀请码">{{ detailUser.shareCode || '-' }}</el-descriptions-item>

          <!-- 新增的5个字段 -->
          <el-descriptions-item label="是否带单员">
            <el-tag :type="detailUser.isLeader === 1 ? 'success' : 'info'">
              {{ detailUser.isLeader === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="一键跟单">
            <el-tag :type="detailUser.isFollowing === 1 ? 'success' : 'info'">
              {{ detailUser.isFollowing === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="跟单时间">
            {{ detailUser.followStartTime ? formatDateTime(detailUser.followStartTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="储备金(USDT)">
            <span style="color: #67C23A">{{ formatNumber(detailUser.reserveAmount) || '0' }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="带单员邮箱">
            {{ detailUser.leaderEmail || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="利润划转">
            <el-tag :type="detailUser.profitTransferEnabled === 1 ? 'success' : 'danger'">
              {{ detailUser.profitTransferEnabled === 1 ? '允许' : '禁止' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-dialog>

      <!-- 用户充值对话框 -->
      <el-dialog
        title="用户充值"
        :visible.sync="rechargeVisible"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="rechargeForm"
          :model="rechargeForm"
          :rules="rechargeRules"
          label-width="100px"
        >
          <el-form-item label="用户手机号">
            <span>{{ rechargeUser.phone }}</span>
          </el-form-item>
          <el-form-item label="账户资金余额">
            <span style="color: #67C23A">{{ formatNumber(rechargeUser.availableBalance) || '0' }}USDT</span>
          </el-form-item>
          <el-form-item label="充值金额" prop="amount">
            <el-input-number
              v-model="rechargeForm.amount"
              :precision="2"
              :step="100"
              style="width: 200px"
            />
            <div style="color:#999;font-size:12px;">可输入负数进行扣款</div>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="rechargeForm.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入充值备注"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="rechargeVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitRecharge">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 银行卡列表对话框 -->
      <el-dialog
        title="提现钱包地址列表"
        :visible.sync="bankCardsVisible"
        width="1000px"
        :close-on-click-modal="false"
      >
        <el-table
          :data="bankCards"
          border
          style="width: 100%"
          v-loading="bankCardsLoading"
        >
          <el-table-column label="钱包地址" prop="chainAddress" align="center" width="360" />   
          <el-table-column label="链名称" prop="chainName" align="center" width="150" />   
          <el-table-column label="是否默认" prop="isDefault" align="center" width="120" />
          <el-table-column label="创建时间" prop="createTime" align="center" width="160" />
          <el-table-column label="更新时间" prop="updateTime" align="center" width="160" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="bankCardsVisible = false">关 闭</el-button>
        </div>
      </el-dialog>

     

      <!-- 修改余额对话框 -->
      <el-dialog
        title="修改账户余额"
        :visible.sync="modifyBalanceVisible"
        width="400px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="modifyBalanceForm"
          :model="modifyBalanceForm"
          :rules="modifyBalanceRules"
          label-width="100px"
        >
          <el-form-item label="用户名">
            <span>{{ currentUser.username }}</span>
          </el-form-item>
          <el-form-item label="手机号">
            <span>{{ currentUser.phone }}</span>
          </el-form-item>
          <el-form-item label="当前余额">
            <span style="color: #67C23A">¥{{ formatNumber(currentUser.availableBalance) || '0' }}</span>
          </el-form-item>
          <el-form-item label="新余额" prop="newBalance">
            <el-input-number
              v-model="modifyBalanceForm.newBalance"
              :precision="2"
              :step="100"
              :controls-position="'right'"
              :min="-999999999"
              style="width: 200px"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="modifyBalanceVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitModifyBalance">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 钱包地址列表对话框 -->
      <el-dialog
        title="充值钱包地址列表"
        :visible.sync="walletDialogVisible"
        width="1000px"
        :close-on-click-modal="false"
      >
        <el-table :data="walletList" v-loading="walletLoading" border>
          <el-table-column label="链名称" prop="chainName" align="center" />
          <el-table-column label="链地址" prop="chainAddress" align="center">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.chainAddress" placement="top">
                <span style="cursor:pointer;">
                  {{ formatAddress(scope.row.chainAddress) }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="BNB余额" prop="bnbBalance" align="center" />
          <el-table-column label="USDT余额" prop="usdtBalance" align="center" />
          <el-table-column label="创建时间" prop="createTime" align="center">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="更新时间" prop="updateTime" align="center">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="walletDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { getUserList, getUserDetail, updateUserStatus, updateUserActivatedStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels, updateUserLevel, updateUserBalance, updateUserGbDividend, deleteUser, updateUserProfitTransferStatus } from '@/api/user/user'
import { listUserWallet } from '@/api/user/wallet'
import { parseTime, formatDate } from '@/utils/date'
import axios from 'axios'

export default {
  name: 'UserList',
  data() {
    return {
      // 查询参数
      listQuery: {
        page: 1,
        limit: 10,
        username: '',      // 用户名/手机号
        userNo: '',        // UID
        status: '',        // 状态
        shareCode: '',     // 邀请码
        referrerEmail: '', // 邀请人邮箱
        email: '',         // 邮箱
        dateRange: [],
        startDate: '',
        endDate: '',

      },
 
      loading: false,
      total: 0,
      tableData: [],
      // 充值相关
      rechargeVisible: false,
      rechargeUser: {},
      rechargeForm: {
        amount: 100,
        remark: ''
      },
      rechargeRules: {
        amount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' }
        ]
      },
      // 详情相关
      detailVisible: false,
      detailUser: {
        username: '',
        phone: '',
        realName: '',
      
        teamCount: 0,
        teamPerformance: 0,
        createTime: '',
        lastLoginTime: '',
        balance: 0,
        status: '1',
        referrer: '',
        inviteCode: '',
        totalRecharge: 0,
        totalWithdraw: 0,
        commission: 0
      },
      // 银行卡相关
      bankCardsVisible: false,
      bankCardsLoading: false,
      bankCards: [],
      // 修改等级相关
      changeLevelVisible: false,
      currentUser: {},
      levelForm: {
        isManager: ''
      },
   
    
      // 修改余额相关
      modifyBalanceVisible: false,
      modifyBalanceForm: {
        newBalance: 0
      },
      modifyBalanceRules: {
        newBalance: [
          { required: true, message: '请输入新余额', trigger: 'blur' }
        ]
      },
      walletDialogVisible: false,
      walletList: [],
      walletLoading: false,
    }
  },
  created() {
    this.getList()
   
  },
  watch: {
    // 监听日期范围变化
    'listQuery.dateRange'(val) {
      if (val && val.length === 2) {
        this.listQuery.startDate = formatDate(val[0])
        this.listQuery.endDate = formatDate(val[1])
      } else {
        this.listQuery.startDate = ''
        this.listQuery.endDate = ''
      }
    }
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try { 
        const res = await getUserList(this.listQuery)  
        if (res.code === 0 || res.code === 200) {
          // 确保数据存在
          if (res.data) {
            this.tableData = res.data.records || []
            this.total = res.data.total || 0 
          } else {
            this.tableData = []
            this.total = 0
          }
        } else {
          this.$message.error(res.msg || '获取用户列表失败')
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      this.getList()
    },

    // 重置查询
    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 10,
        username: '',
        userNo: '',        // 重置UID
        status: '',
        isManager: '',     // 重置用户等级
        shareCode: '',     // 重置邀请码
        referrerEmail: '', // 重置邀请人邮箱
        email: '',         // 重置邮箱
        dateRange: [],
        startDate: '',
        endDate: '',
        contractAgreement: '', // 重置合同状态
        isGbDividend: ''      // 重置GB分红状态
      }
      this.getList()
    },

    // 格式化数字
    formatNumber(num) {
      return num ? num.toLocaleString() : '0'
    },

    // 获取等级名称
    getLevelName(level) {
      const levelMap = {
        0: '普通会员',
        1: '合伙人',
        2: '联创'
      }
      return levelMap[level] || '未知等级'
    },

    // 获取等级标签类型
    getLevelType(level) {
      const typeMap = {
        0: 'info',
        1: 'success',
        2: 'warning'
      }
      return typeMap[level] || 'info'
    },

    // 处理状态变更
    async handleStatusChange(row) {
      try {
        const res = await updateUserStatus(row.id, row.status)
        if (res.code === 0 || res.code === 200) {
          this.$message.success(`${row.status === 1 ? '启用' : '禁用'}成功`)
        } else {
          row.status = row.status === 1 ? 0 : 1
          this.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        row.status = row.status === 1 ? 0 : 1
        this.$message.error('操作失败')
      }
    },

    // 处理激活状态变更
    async handleActivatedChange(row) {
      try {
        const res = await updateUserActivatedStatus(row.id, row.isActivated)
        if (res.code === 0 || res.code === 200) {
          this.$message.success(`${row.isActivated === 1 ? '激活' : '取消激活'}成功`)
        } else {
          row.isActivated = row.isActivated === 1 ? 0 : 1
          this.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        row.isActivated = row.isActivated === 1 ? 0 : 1
        this.$message.error('操作失败')
      }
    },

    // 处理利润划转状态变更
    async handleProfitTransferChange(row) {
      try {
        const res = await updateUserProfitTransferStatus(row.id, row.profitTransferEnabled)
        if (res.code === 0 || res.code === 200) {
          this.$message.success(`${row.profitTransferEnabled === 1 ? '开启' : '关闭'}利润划转成功`)
        } else {
          row.profitTransferEnabled = row.profitTransferEnabled === 1 ? 0 : 1
          this.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        row.profitTransferEnabled = row.profitTransferEnabled === 1 ? 0 : 1
        this.$message.error('操作失败')
      }
    },

    // 查看详情
    async handleDetail(row) {
      try {
        const res = await getUserDetail(row.id)
        if (res.code === 0 || res.code === 200) {
          this.detailUser = res.data
          this.detailVisible = true
        } else {
          this.$message.error(res.msg || '获取详情失败')
        }
      } catch (error) {
        this.$message.error('获取详情失败')
      }
    },

    // 打开充值对话框
    handleRecharge(row) {
      this.rechargeUser = row
      this.rechargeForm = {
        amount: 100,
        remark: ''
      }
      this.rechargeVisible = true
    },

    // 提交充值
    submitRecharge() {
      this.$refs.rechargeForm.validate(async valid => {
        if (valid) {
          try {
            const res = await rechargeUser(this.rechargeUser.id, this.rechargeForm)
            if (res.code === 0 || res.code === 200) {
              this.$message.success('充值成功')
              this.rechargeVisible = false
              this.getList() // 刷新列表
            } else {
              this.$message.error(res.msg || '充值失败')
            }
          } catch (error) {
            this.$message.error('充值失败')
          }
        }
      })
    },

    // 分页相关
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },

    // 格式化日期时间
    formatDateTime(time) {
      if (!time) return ''
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    },
    
    // 格式化日期
    formatDate(time) {
      if (!time) return ''
      return parseTime(time, 'yyyy-MM-dd')
    },

    // 重置密码
    handleReset(row) {
      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await resetUserPassword(row.id)
          if (res.code === 0 || res.code === 200) {
            this.$message.success('密码重置成功')
          } else {
            this.$message.error(res.msg || '密码重置失败')
          }
        } catch (error) {
          this.$message.error('密码重置失败')
        }
      }).catch(() => {
        // 取消重置，不做任何操作
      })
    },

    // 查看银行
    async handleBankCards(row) {
      
      this.bankCardsVisible = true
      this.bankCardsLoading = true
      try {
        const res = await getUserBankCards(row.id)
       
        if (res.code === 0 || res.code === 200) {
          this.bankCards = res.data || []
        } else {
          this.$message.error(res.msg || '获取银行卡列表失败')
        }
      } catch (error) {
        console.error('获取银行卡失败:', error)  // 添加错误日志
        this.$message.error('获取银行卡列表失败')
      } finally {
        this.bankCardsLoading = false
      }
    },
 
    // 打开修改等级对话框
    handleChangeLevel(row) {
      this.currentUser = row
      this.levelForm.isManager = row.isManager
      this.changeLevelVisible = true
    },
    
    // 提交修改等级
    submitChangeLevel() {
      this.$refs.levelForm.validate(async valid => {
        if (valid) {
          try {
            const res = await updateUserLevel(
              this.currentUser.id,
              this.levelForm.isManager
            )
            if (res.code === 0) {
              this.$message.success('修改等级成功')
              this.changeLevelVisible = false
              this.getList() // 刷新列表
            } else {
              this.$message.error(res.msg || '修改等级失败')
            }
          } catch (error) {
            console.error('修改等级失败:', error)
            this.$message.error('修改等级失败')
          }
        }
      })
    },

    // 打开修改余额对话框
    handleModifyBalance(row) {
      this.currentUser = row
      this.modifyBalanceForm.newBalance = row.availableBalance
      this.modifyBalanceVisible = true
    },

    // 提交修改余额
    submitModifyBalance() {
      this.$refs.modifyBalanceForm.validate(async valid => {
        if (valid) {
          try {
            console.log('准备修改余额:', {
              userId: this.currentUser.id,
              newBalance: this.modifyBalanceForm.newBalance
            })
            
            const res = await updateUserBalance(
              this.currentUser.id, 
              this.modifyBalanceForm.newBalance
            )
            
            console.log('修改余额响应:', res)
            
            if (res.code === 0 || res.code === 200) {
              this.$message.success('修改余额成功')
              this.modifyBalanceVisible = false
              this.getList() // 刷新列表
            } else {
              this.$message.error(res.msg || '修改余额失败')
            }
          } catch (error) {
            console.error('修改余额失败:', error)
            this.$message.error('修改余额失败')
          }
        }
      })
    },

    // 处理GB分红状态变更
    async handleGbDividendChange(row) {
      try {
        const res = await updateUserGbDividend(row.id, row.isGbDividend)
        if (res.code === 0 || res.code === 200) {
          this.$message.success(`${row.isGbDividend === 1 ? '开启' : '关闭'}GB分红成功`)
        } else {
          row.isGbDividend = row.isGbDividend === 1 ? 0 : 1
          this.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        row.isGbDividend = row.isGbDividend === 1 ? 0 : 1
        this.$message.error('操作失败')
      }
    },

    // 处理删除用户
    handleDelete(row) {
      this.$confirm('确认要删除该用户吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteUser(row.id)
          if (res.code === 0 || res.code === 200) {
            this.$message.success('删除成功')
            this.getList() // 刷新列表
          } else {
            this.$message.error(res.msg || '删除失败')
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 取消删除，不做任何操作
      })
    },

    async handleWalletList(row) {
      this.walletDialogVisible = true;
      this.walletLoading = true;
      try {
        const res = await listUserWallet({ userId: row.id, pageNum: 1, pageSize: 100 });
        if (res.code === 0 && res.data && res.data.records) {
          this.walletList = res.data.records;
        } else {
          this.walletList = [];
        }
      } catch (e) {
        this.walletList = [];
        this.$message.error('获取钱包列表失败');
      }
      this.walletLoading = false;
    },

    formatAddress(addr) {
      if (!addr) return '';
      if (addr.length <= 16) return addr;
      return addr.slice(0, 6) + '...' + addr.slice(-6);
    },

    // 表格序号方法，保证翻页不间断
    indexMethod(index) {
      return (this.listQuery.page - 1) * this.listQuery.limit + index + 1;
    },
  }
}
</script>

<style lang="scss" scoped>
/* 黑金主题整体覆盖 */
.app-container {
  background: #181818;
  min-height: 100vh;
}
.el-card.box-card {
  background: #232323 !important;
  border: 1.5px solid #FFD700 !important;
  border-radius: 12px;
  color: #fff;
  box-shadow: 0 4px 24px #000a;
}
.filter-container {
  background: transparent;
  .el-input__inner, .el-select .el-input__inner {
    background: #232323 !important;
    border: 1.5px solid #FFD700 !important;
    color: #fff !important;
    &::placeholder {
      color: #b3b3b3 !important;
    }
  }
  .el-select-dropdown {
    background: #232323 !important;
    color: #fff !important;
    .el-select-dropdown__item {
      color: #fff !important;
      &:hover, &.selected {
        color: #FFD700 !important;
        background: #181818 !important;
      }
    }
  }
  .el-date-editor {
    background: #232323 !important;
    border: 1.5px solid #FFD700 !important;
    color: #fff !important;
    .el-input__inner {
      background: #232323 !important;
      color: #fff !important;
      border: none !important;
    }
    .el-input__prefix, .el-input__suffix {
      color: #FFD700 !important;
    }
  }
  .el-date-editor .el-range-separator {
    color: #FFD700 !important;
  }
  .el-button {
    background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;
    color: #181818 !important;
    border: none !important;
    font-weight: bold;
    border-radius: 8px;
    &:hover {
      background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%) !important;
      color: #000 !important;
    }
  }
}
.el-table {
  background: #181818 !important;
  color: #fff !important;
  th, td {
    background: #232323 !important;
    color: #fff !important;
    border-color: #FFD70033 !important;
  }
  td {
    color: #fff !important;
  }
  td .is-disabled, td .is-disabled * {
    color: #b3b3b3 !important;
  }
  .el-table__body tr:hover > td {
    background: #292929 !important;
  }
  .el-table__header th {
    background: #181818 !important;
    color: #FFD700 !important;
    font-weight: bold;
  }
}
.el-table .el-button[type="text"] {
  color: #FFD700 !important;
  font-weight: bold;
}
.el-tag.el-tag--info {
  background: #232323 !important;
  color: #FFD700 !important;
  border: 1px solid #FFD700 !important;
}
.el-switch {
  .el-switch__core {
    background: #232323 !important;
    border: 1.5px solid #FFD700 !important;
  }
  .el-switch__button {
    background: #FFD700 !important;
  }
}
.el-pagination {
  background: transparent;
  .el-pager li {
    color: #fff !important;
    background: #232323 !important;
    border: 1px solid #FFD700 !important;
    &.active {
      color: #181818 !important;
      background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;
      border: 1px solid #FFD700 !important;
    }
  }
  .el-pagination__total, .el-pagination__jump, .el-pagination__sizes {
    color: #fff !important;
  }
}
// 滚动条黑金化
::-webkit-scrollbar {
  height: 8px;
  width: 8px;
  background: #232323;
}
::-webkit-scrollbar-thumb {
  background: #FFD70033;
  border-radius: 4px;
}
// 弹窗黑金主题
::v-deep .el-dialog {
  background: #181818 !important;
  border-radius: 12px;
  .el-dialog__header {
    color: #fff !important;
    font-size: 20px;
    font-weight: bold;
    opacity: 1 !important;
    border-bottom: 1px solid #FFD70033;
  }
  .el-dialog__title {
    color: #fff !important;
    font-size: 20px;
    font-weight: bold;
    opacity: 1 !important;
  }
  .el-dialog__footer {
    .el-button--primary {
      background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%);
      color: #181818;
      border: none;
      font-weight: bold;
      &:hover {
        background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%);
        color: #000;
      }
    }
  }
  .el-form-item__label {
    color: #FFD700;
  }
  .el-input__inner {
    background: #232323;
    border: 1.5px solid #FFD700;
    color: #fff;
  }
}

.filter-container {
  padding-bottom: 10px;
  
  .filter-row {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .filter-item {
    width: 100%;
  }
  
  .date-range-picker {
    width: 100%;
  }
  
  .el-button {
    margin-right: 10px;
  }
  
  .el-select {
    width: 100%;
  }
}

.pagination-container {
  padding: 10px 0;
}

// 修改对话框样式
.user-detail-dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
  }
  
  ::v-deep .el-dialog__header {
    padding: 15px 20px 10px;
  }
  
  ::v-deep .el-dialog__footer {
    padding: 10px 20px 15px;
  }
}

.el-descriptions {
  margin: 0;
}

.dialog-footer {
  text-align: right;
  padding-top: 0;
}

// 日期选择器弹窗黑金化
::v-deep .el-picker-panel {
  background: #232323 !important;
  color: #fff !important;
  border: 1.5px solid #FFD700 !important;
  .el-date-table th {
    color: #FFD700 !important;
  }
  .el-date-table td {
    color: #fff !important;
    &.in-range {
      background: #FFD70022 !important;
    }
    &.current, &.today {
      color: #FFD700 !important;
      font-weight: bold;
    }
    &.available:hover {
      background: #FFD70033 !important;
      color: #FFD700 !important;
    }
  }
  .el-picker-panel__footer {
    background: #232323 !important;
    border-top: 1px solid #FFD70033 !important;
    .el-button--text, .el-button--default {
      color: #FFD700 !important;
    }
  }
}

// 强化表格内容字体色
::v-deep .el-table td, ::v-deep .el-table td * {
  color: #fff !important;
}

.el-table__fixed-body-wrapper td,
.el-table__fixed-body-wrapper td *,
.el-table__fixed .cell,
.el-table__fixed .cell * {
  color: #fff !important;
  -webkit-text-fill-color: #fff !important;
}
</style>

<style lang="scss">
.el-table__body td,
.el-table__body td *,
.el-table__body .cell,
.el-table__body .cell * {
  color: #fff !important;
  -webkit-text-fill-color: #fff !important;
}
.el-table__body td .is-disabled,
.el-table__body td .is-disabled *,
.el-table__body td[style*='color: #ccc'],
.el-table__body td[style*='color: #e0e0e0'] {
  color: #b3b3b3 !important;
  -webkit-text-fill-color: #b3b3b3 !important;
}
</style>

<style lang="scss">
/* 黑金主题 el-descriptions-item label 全覆盖 */
.el-descriptions__label,
.el-descriptions-item__label,
.el-descriptions-item__cell.is-label {
  background: #181818 !important;
  color: #FFD700 !important;
  font-weight: bold !important;
  border-right: 1px solid #FFD70033 !important;
}
</style>
